package corp.jamaro.jamaroservidor.app.sunat;

// es el primer "comprobante" a partir de este luego podremos crear boletas y facturas

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.UUID;
@Data
@Node
public class Ticket {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;




}
