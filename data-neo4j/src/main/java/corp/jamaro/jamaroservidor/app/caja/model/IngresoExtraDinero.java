package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class IngresoExtraDinero {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String motivo;
    private Double monto;//el monto en Soles ya que el sistema trabajar por defecto en soles.
    
    @Relationship(type = "REALIZADO_POR")
    private User realizadoPor;

    private Instant realizadoEl = Instant.now();

    @Relationship(type = "CON_DINERO_INGRESADO")//dinero del tipo esEntrada true
    private Set<Dinero> dineroIngresado;

}
