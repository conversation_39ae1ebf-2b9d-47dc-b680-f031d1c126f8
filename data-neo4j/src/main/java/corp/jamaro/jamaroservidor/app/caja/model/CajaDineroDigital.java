package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class CajaDineroDigital {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String cuentaDigitalAsignada;//detalles de la cuenta digital asignada, número de cuenta, etc.

    //inicio
    @Relationship(type = "ABRE_CAJA_DINERO_DIGITAL")
    private User abiertaPor;
    private Instant abiertaEl = Instant.now();
    private Double montoInicialDigital; // en soles

    //Cierre digital
    @Relationship(type = "CIERRE_CAJA_DINERO_DIGITAL")
    private User cerradaPor;
    private Instant cerradaEl;

    private Double cierreDigitalCalculado; // totalEntradasDigital - totalSalidasDigital + montoInicialDigital
    private Double cierreDigitalDeclarado;
    private Double diferenciaCierreDigital;

    //CAMPOS PARA CALCULO
    private Double totalEntradasDigital;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = true y tipoDeDinero = DIGITAL
    private Double totalSalidasDigital;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = false y tipoDeDinero = DIGITAL

    //Datos extra de interés que no se usan pal cálculo de cierre, pero si para estadísticas.
    private Double pagosVentaContadoDigital;
    private Double pagosVentaCreditoDigital;
    private Double pagosVentaPedidoDigital;
    private Double devolucionesVentaDigital;
    private Double ingresosExtraDigital;
    private Double gastosExtraDigital;
    private Double pagosDineroProgramadoDigital;

    @Relationship(type = "CON_ENTRADA_CAJA_DIGITAL")
    private Set<Dinero> entradasDigital;

    @Relationship(type = "CON_SALIDA_CAJA_DIGITAL")
    private Set<Dinero> salidasDigital;

    private Boolean estaCerrada = false;
}
