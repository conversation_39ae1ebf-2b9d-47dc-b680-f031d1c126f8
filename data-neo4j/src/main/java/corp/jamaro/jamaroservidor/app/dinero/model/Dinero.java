package corp.jamaro.jamaroservidor.app.dinero.model;


import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class Dinero {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "TRAMITADO_POR")
    private User tramitadoPor;

    private Double monto;//es el monto real en soles, con este dato hace calculos el sistema

    private TipoDeDinero tipoDeDinero;

    private String detalles; // no siempre es necesario detalles de tipo de pago por ejemplo si es digital, yape, transferencia, etc, si es efectivo series de moneda entre otros

    private Instant createdAt=Instant.now();

    //recuerda que los cuadres de caja se hacen con entradas menos salidas de dinero
    private Boolean esEntrada;// true (entrada) false (salida)


    public enum TipoDeDinero {
        EFECTIVO,
        DIGITAL
    }

}
