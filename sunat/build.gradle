plugins {
    id 'java-library'
    id 'io.spring.dependency-management' version '1.1.6'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.0"
    }
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // SUNAT XBuilder and XSender dependencies
    api 'io.github.project-openubl:xbuilder:5.0.2'
    api 'io.github.project-openubl:spring-boot-xsender:5.0.2'
    
    // Spring Boot starters
    api 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    
    // Dependencies on other local modules
    implementation project(':data-neo4j')
    implementation project(':security') // For authentication and authorization
    
    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    
    // Additional dependencies for XSender (as mentioned in the documentation)
    runtimeOnly 'jakarta.xml.soap:jakarta.xml.soap-api:1.4.2'
    runtimeOnly 'jakarta.xml.ws:jakarta.xml.ws-api:2.3.3'
    runtimeOnly 'jakarta.annotation:jakarta.annotation-api:1.3.5'
    
    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
    useJUnitPlatform()
}