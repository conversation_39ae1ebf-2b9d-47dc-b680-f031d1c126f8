package corp.jamaro.jamaroservidor.sunat.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * DTO base para solicitudes de generación de documentos SUNAT.
 * Contiene la información común necesaria para generar cualquier tipo de documento.
 */
@Data
@Builder
public class SunatDocumentRequest {
    
    // Información del documento
    private String serie;
    private Long numero;
    private LocalDate fechaEmision;
    private String moneda;
    private String observaciones;
    
    // Información del proveedor (emisor)
    private ProveedorInfo proveedor;
    
    // Información del cliente (receptor)
    private ClienteInfo cliente;
    
    // Detalles del documento
    private List<DetalleInfo> detalles;
    
    // ID del comprobante en la base de datos (para referencia)
    private UUID comprobanteId;
    
    /**
     * Información del proveedor/emisor del documento.
     */
    @Data
    @Builder
    public static class ProveedorInfo {
        private String ruc;
        private String razonSocial;
        private String nombreComercial;
        private DireccionInfo direccion;
        private ContactoInfo contacto;
    }
    
    /**
     * Información del cliente/receptor del documento.
     */
    @Data
    @Builder
    public static class ClienteInfo {
        private String tipoDocumentoIdentidad;
        private String numeroDocumentoIdentidad;
        private String nombre;
        private String razonSocial;
        private DireccionInfo direccion;
        private ContactoInfo contacto;
    }
    
    /**
     * Información de dirección.
     */
    @Data
    @Builder
    public static class DireccionInfo {
        private String ubigeo;
        private String departamento;
        private String provincia;
        private String distrito;
        private String direccion;
        private String codigoLocal;
        private String urbanizacion;
        private String codigoPais;
    }
    
    /**
     * Información de contacto.
     */
    @Data
    @Builder
    public static class ContactoInfo {
        private String telefono;
        private String email;
    }
    
    /**
     * Información de detalle/item del documento.
     */
    @Data
    @Builder
    public static class DetalleInfo {
        private String codigo;
        private String descripcion;
        private Double cantidad;
        private String unidadMedida;
        private Double precio;
        private Boolean precioConImpuestos;
        private String igvTipo;
        private String iscTipo;
        private Double tasaIsc;
        private Boolean icbAplica;
        private Double descuento;
    }
}