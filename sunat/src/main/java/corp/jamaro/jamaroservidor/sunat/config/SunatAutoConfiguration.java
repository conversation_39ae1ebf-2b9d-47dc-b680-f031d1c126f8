package corp.jamaro.jamaroservidor.sunat.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Configuración automática para el módulo SUNAT.
 * Esta clase habilita automáticamente todos los componentes del módulo SUNAT
 * cuando la propiedad sunat.enabled está configurada como true.
 */
@Configuration
@ComponentScan(basePackages = {
    "corp.jamaro.jamaroservidor.sunat.config",
    "corp.jamaro.jamaroservidor.sunat.service",
    "io.github.project.openubl.spring.xsender.runtime"
})
@ConditionalOnProperty(name = "sunat.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class SunatAutoConfiguration {

    public SunatAutoConfiguration() {
        log.info("Inicializando configuración automática del módulo SUNAT");
        log.info("Componentes SUNAT habilitados:");
        log.info("  - SunatConfig: Configuración de XBuilder y XSender");
        log.info("  - BaseDocumentBuilderService: Generación de documentos XML");
        log.info("  - BaseDocumentSenderService: Envío de documentos a SUNAT");
        log.info("  - InvoiceService: Servicio de facturas");
        log.info("  - BoletaService: Servicio de boletas");
        log.info("  - CreditNoteService: Servicio de notas de crédito");
        log.info("  - DebitNoteService: Servicio de notas de débito");
        log.info("  - VoidedDocumentsService: Servicio de comunicaciones de baja");
        log.info("  - SummaryDocumentsService: Servicio de resúmenes diarios");
        log.info("  - PerceptionService: Servicio de percepciones");
        log.info("  - RetentionService: Servicio de retenciones");
        log.info("  - DespatchAdviceService: Servicio de guías de remisión");
        log.info("  - XSender Runtime: Componentes de Apache Camel para envío");
    }
}