package corp.jamaro.jamaroservidor.sunat.service.builder;

import io.github.project.openubl.xbuilder.content.models.standard.general.CreditNote;
import io.github.project.openubl.xbuilder.content.models.standard.general.DebitNote;
import io.github.project.openubl.xbuilder.content.models.standard.general.Invoice;
import io.github.project.openubl.xbuilder.content.models.standard.guia.DespatchAdvice;
import io.github.project.openubl.xbuilder.content.models.sunat.baja.VoidedDocuments;
import io.github.project.openubl.xbuilder.content.models.sunat.percepcionretencion.Perception;
import io.github.project.openubl.xbuilder.content.models.sunat.percepcionretencion.Retention;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.SummaryDocuments;
import io.github.project.openubl.xbuilder.enricher.ContentEnricher;
import io.github.project.openubl.xbuilder.enricher.config.DateProvider;
import io.github.project.openubl.xbuilder.enricher.config.Defaults;
import io.github.project.openubl.xbuilder.renderer.TemplateProducer;
import io.github.project.openubl.xbuilder.signature.CertificateDetails;
import io.github.project.openubl.xbuilder.signature.CertificateDetailsFactory;
import io.github.project.openubl.xbuilder.signature.XMLSigner;
import io.github.project.openubl.xbuilder.signature.XmlSignatureHelper;
import io.quarkus.qute.Template;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;

/**
 * Servicio base para la generación de documentos XML usando XBuilder.
 * Proporciona funcionalidades comunes para todos los tipos de documentos SUNAT.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaseDocumentBuilderService {

    private final Defaults defaults;
    private final DateProvider dateProvider;

    @Value("${sunat.certificate.path:LLAMA-PE-CERTIFICADO-DEMO-12345678912.pfx}")
    private String certificatePath;

    @Value("${sunat.certificate.password:password}")
    private String certificatePassword;

    @Value("${sunat.signature.id:Project OpenUBL}")
    private String signatureId;

    /**
     * Enriquece una factura con valores calculados automáticamente.
     */
    public void enrichInvoice(Invoice invoice) {
        log.debug("Enriqueciendo Invoice");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(invoice);
        log.debug("Invoice enriquecida exitosamente");
    }

    /**
     * Enriquece una nota de crédito con valores calculados automáticamente.
     */
    public void enrichCreditNote(CreditNote creditNote) {
        log.debug("Enriqueciendo CreditNote");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(creditNote);
        log.debug("CreditNote enriquecida exitosamente");
    }

    /**
     * Enriquece una nota de débito con valores calculados automáticamente.
     */
    public void enrichDebitNote(DebitNote debitNote) {
        log.debug("Enriqueciendo DebitNote");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(debitNote);
        log.debug("DebitNote enriquecida exitosamente");
    }

    /**
     * Enriquece un documento de baja con valores calculados automáticamente.
     */
    public void enrichVoidedDocuments(VoidedDocuments voidedDocuments) {
        log.debug("Enriqueciendo VoidedDocuments");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(voidedDocuments);
        log.debug("VoidedDocuments enriquecido exitosamente");
    }

    /**
     * Enriquece un resumen diario con valores calculados automáticamente.
     */
    public void enrichSummaryDocuments(SummaryDocuments summaryDocuments) {
        log.debug("Enriqueciendo SummaryDocuments");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(summaryDocuments);
        log.debug("SummaryDocuments enriquecido exitosamente");
    }

    /**
     * Enriquece una percepción con valores calculados automáticamente.
     */
    public void enrichPerception(Perception perception) {
        log.debug("Enriqueciendo Perception");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(perception);
        log.debug("Perception enriquecida exitosamente");
    }

    /**
     * Enriquece una retención con valores calculados automáticamente.
     */
    public void enrichRetention(Retention retention) {
        log.debug("Enriqueciendo Retention");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(retention);
        log.debug("Retention enriquecida exitosamente");
    }

    /**
     * Enriquece una guía de remisión con valores calculados automáticamente.
     */
    public void enrichDespatchAdvice(DespatchAdvice despatchAdvice) {
        log.debug("Enriqueciendo DespatchAdvice");
        ContentEnricher enricher = new ContentEnricher(defaults, dateProvider);
        enricher.enrich(despatchAdvice);
        log.debug("DespatchAdvice enriquecida exitosamente");
    }

    /**
     * Genera XML sin firmar para una factura.
     *
     * @param invoice El objeto Invoice
     * @return XML sin firmar
     */
    public String generateInvoiceXML(Object invoice) {
        log.debug("Generando XML para Invoice");
        Template template = TemplateProducer.getInstance().getInvoice();
        String xml = template.data(invoice).render();
        log.debug("XML generado exitosamente para Invoice");
        return xml;
    }

    /**
     * Genera XML sin firmar para una nota de crédito.
     *
     * @param creditNote El objeto CreditNote
     * @return XML sin firmar
     */
    public String generateCreditNoteXML(Object creditNote) {
        log.debug("Generando XML para CreditNote");
        Template template = TemplateProducer.getInstance().getCreditNote();
        String xml = template.data(creditNote).render();
        log.debug("XML generado exitosamente para CreditNote");
        return xml;
    }

    /**
     * Genera XML sin firmar para una nota de débito.
     *
     * @param debitNote El objeto DebitNote
     * @return XML sin firmar
     */
    public String generateDebitNoteXML(Object debitNote) {
        log.debug("Generando XML para DebitNote");
        Template template = TemplateProducer.getInstance().getDebitNote();
        String xml = template.data(debitNote).render();
        log.debug("XML generado exitosamente para DebitNote");
        return xml;
    }

    /**
     * Genera XML sin firmar para un documento de baja.
     *
     * @param voidedDocument El objeto VoidedDocuments
     * @return XML sin firmar
     */
    public String generateVoidedDocumentXML(Object voidedDocument) {
        log.debug("Generando XML para VoidedDocument");
        Template template = TemplateProducer.getInstance().getVoidedDocument();
        String xml = template.data(voidedDocument).render();
        log.debug("XML generado exitosamente para VoidedDocument");
        return xml;
    }

    /**
     * Genera XML sin firmar para un resumen diario.
     *
     * @param summaryDocument El objeto SummaryDocuments
     * @return XML sin firmar
     */
    public String generateSummaryDocumentXML(Object summaryDocument) {
        log.debug("Generando XML para SummaryDocument");
        Template template = TemplateProducer.getInstance().getSummaryDocuments();
        String xml = template.data(summaryDocument).render();
        log.debug("XML generado exitosamente para SummaryDocument");
        return xml;
    }

    /**
     * Genera XML sin firmar para una percepción.
     *
     * @param perception El objeto Perception
     * @return XML sin firmar
     */
    public String generatePerceptionXML(Object perception) {
        log.debug("Generando XML para Perception");
        Template template = TemplateProducer.getInstance().getPerception();
        String xml = template.data(perception).render();
        log.debug("XML generado exitosamente para Perception");
        return xml;
    }

    /**
     * Genera XML sin firmar para una retención.
     *
     * @param retention El objeto Retention
     * @return XML sin firmar
     */
    public String generateRetentionXML(Object retention) {
        log.debug("Generando XML para Retention");
        Template template = TemplateProducer.getInstance().getRetention();
        String xml = template.data(retention).render();
        log.debug("XML generado exitosamente para Retention");
        return xml;
    }

    /**
     * Genera XML sin firmar para una guía de remisión.
     *
     * @param despatchAdvice El objeto DespatchAdvice
     * @return XML sin firmar
     */
    public String generateDespatchAdviceXML(Object despatchAdvice) {
        log.debug("Generando XML para DespatchAdvice");
        Template template = TemplateProducer.getInstance().getDespatchAdvice();
        String xml = template.data(despatchAdvice).render();
        log.debug("XML generado exitosamente para DespatchAdvice");
        return xml;
    }

    /**
     * Firma un XML usando el certificado configurado.
     *
     * @param xml El XML a firmar
     * @return XML firmado como String
     * @throws Exception Si ocurre un error durante la firma
     */
    public String signXML(String xml) throws Exception {
        log.debug("Firmando XML con certificado: {}", certificatePath);
        
        try (InputStream ksInputStream = getClass().getClassLoader().getResourceAsStream(certificatePath)) {
            if (ksInputStream == null) {
                throw new IllegalArgumentException("No se pudo encontrar el certificado: " + certificatePath);
            }

            CertificateDetails certificate = CertificateDetailsFactory.create(ksInputStream, certificatePassword);
            X509Certificate x509Certificate = certificate.getX509Certificate();
            PrivateKey privateKey = certificate.getPrivateKey();

            Document signedXML = XMLSigner.signXML(xml, signatureId, x509Certificate, privateKey);
            
            byte[] bytesFromDocument = XmlSignatureHelper.getBytesFromDocument(signedXML);
            String signedXMLString = new String(bytesFromDocument, StandardCharsets.ISO_8859_1);
            
            log.debug("XML firmado exitosamente");
            return signedXMLString;
        }
    }

    /**
     * Genera y firma un XML completo para una factura.
     */
    public String generateAndSignInvoiceXML(Invoice invoice) throws Exception {
        log.info("Generando y firmando Invoice");
        enrichInvoice(invoice);
        String xml = generateInvoiceXML(invoice);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para una nota de crédito.
     */
    public String generateAndSignCreditNoteXML(CreditNote creditNote) throws Exception {
        log.info("Generando y firmando CreditNote");
        enrichCreditNote(creditNote);
        String xml = generateCreditNoteXML(creditNote);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para una nota de débito.
     */
    public String generateAndSignDebitNoteXML(DebitNote debitNote) throws Exception {
        log.info("Generando y firmando DebitNote");
        enrichDebitNote(debitNote);
        String xml = generateDebitNoteXML(debitNote);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para un documento de baja.
     */
    public String generateAndSignVoidedDocumentXML(VoidedDocuments voidedDocuments) throws Exception {
        log.info("Generando y firmando VoidedDocuments");
        enrichVoidedDocuments(voidedDocuments);
        String xml = generateVoidedDocumentXML(voidedDocuments);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para un resumen diario.
     */
    public String generateAndSignSummaryDocumentXML(SummaryDocuments summaryDocuments) throws Exception {
        log.info("Generando y firmando SummaryDocuments");
        enrichSummaryDocuments(summaryDocuments);
        String xml = generateSummaryDocumentXML(summaryDocuments);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para una percepción.
     */
    public String generateAndSignPerceptionXML(Perception perception) throws Exception {
        log.info("Generando y firmando Perception");
        enrichPerception(perception);
        String xml = generatePerceptionXML(perception);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para una retención.
     */
    public String generateAndSignRetentionXML(Retention retention) throws Exception {
        log.info("Generando y firmando Retention");
        enrichRetention(retention);
        String xml = generateRetentionXML(retention);
        return signXML(xml);
    }

    /**
     * Genera y firma un XML completo para una guía de remisión.
     */
    public String generateAndSignDespatchAdviceXML(DespatchAdvice despatchAdvice) throws Exception {
        log.info("Generando y firmando DespatchAdvice");
        enrichDespatchAdvice(despatchAdvice);
        String xml = generateDespatchAdviceXML(despatchAdvice);
        return signXML(xml);
    }
}