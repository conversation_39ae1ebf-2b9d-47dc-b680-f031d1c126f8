package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog1;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog19;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog1_Invoice;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog6;
import io.github.project.openubl.xbuilder.content.models.common.Cliente;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.Comprobante;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.ComprobanteImpuestos;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.ComprobanteValorVenta;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.SummaryDocuments;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.SummaryDocumentsItem;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;

/**
 * Servicio para el manejo de resúmenes diarios (SummaryDocuments) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar resúmenes diarios electrónicos.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SummaryDocumentsService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera un resumen diario XML firmado sin enviarlo a SUNAT.
     *
     * @param request Datos del resumen diario
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateSummaryDocumentsXML(SunatDocumentRequest request) {
        log.info("Generando XML para resumen diario {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder
            SummaryDocuments summaryDocuments = convertToXBuilderSummaryDocuments(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignSummaryDocumentXML(summaryDocuments);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "RESUMEN_DIARIO"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para resumen diario {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para resumen diario {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de resumen diario", e);
        }
    }

    /**
     * Genera y envía un resumen diario a SUNAT.
     * Los resúmenes diarios se procesan de forma asíncrona y devuelven un ticket.
     *
     * @param request Datos del resumen diario
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendSummaryDocuments(SunatDocumentRequest request) {
        log.info("Generando y enviando resumen diario {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateSummaryDocumentsXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT con manejo de tickets
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLWithTicketHandling(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            // Los resúmenes diarios pueden devolver un ticket
            if (sunatResponse.getSunat() != null && sunatResponse.getSunat().getTicket() != null) {
                xmlResponse.setTicket(sunatResponse.getSunat().getTicket());
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.PENDING);
                xmlResponse.setMessage("Resumen diario enviado, procesándose en SUNAT");
                log.info("Resumen diario {}-{} enviado con ticket: {}", 
                        request.getSerie(), request.getNumero(), sunatResponse.getSunat().getTicket());
            } else if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Resumen diario enviado y aceptado por SUNAT");
                log.info("Resumen diario {}-{} enviado exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Resumen diario rechazado por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Resumen diario {}-{} rechazado por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando resumen diario {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando resumen diario a SUNAT", e);
        }
    }

    /**
     * Consulta el estado de un ticket de resumen diario.
     *
     * @param ticket El número de ticket
     * @param originalXmlContent El XML original para obtener la configuración
     * @return Respuesta con el estado del ticket
     */
    public SunatDocumentResponse verifySummaryDocumentsTicket(String ticket, String originalXmlContent) {
        log.info("Consultando ticket de resumen diario: {}", ticket);
        
        try {
            byte[] xmlBytes = originalXmlContent.getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.verifyTicket(ticket, xmlBytes);
            
            SunatDocumentResponse response = SunatDocumentResponse.builder()
                    .ticket(ticket)
                    .sentToSunat(true)
                    .sunatStatus(sunatResponse.getStatus().toString())
                    .sunatMessage(documentSenderService.getErrorMessage(sunatResponse))
                    .build();
            
            if (documentSenderService.isSuccessResponse(sunatResponse)) {
                response.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                response.setMessage("Resumen diario procesado exitosamente por SUNAT");
                log.info("Ticket {} procesado exitosamente", ticket);
            } else {
                response.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                response.setMessage("Error en el procesamiento del resumen diario");
                response.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Error en ticket {}: {}", ticket, response.getErrorMessage());
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("Error consultando ticket {}: {}", ticket, e.getMessage(), e);
            return SunatDocumentResponse.error(null, 
                    "Error consultando ticket de resumen diario", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto SummaryDocuments de XBuilder.
     *
     * @param request El request con los datos del resumen diario
     * @return Objeto SummaryDocuments de XBuilder
     */
    private SummaryDocuments convertToXBuilderSummaryDocuments(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a SummaryDocuments XBuilder");
        
        SummaryDocuments.SummaryDocumentsBuilder summaryBuilder = SummaryDocuments.builder()
                .numero(request.getNumero().intValue())
                .fechaEmisionComprobantes(request.getFechaEmision() != null ? 
                        request.getFechaEmision() : LocalDate.now().minusDays(1));
        
        // Configurar proveedor
        if (request.getProveedor() != null) {
            summaryBuilder.proveedor(convertToXBuilderProveedor(request.getProveedor()));
        }
        
        // Para resúmenes diarios, necesitamos crear items de los comprobantes
        // Por ahora creamos un item de ejemplo basado en los datos del request
        SummaryDocumentsItem item = createSummaryDocumentItem(request);
        summaryBuilder.comprobante(item);
        
        return summaryBuilder.build();
    }

    /**
     * Crea un item de resumen diario basado en el request.
     */
    private SummaryDocumentsItem createSummaryDocumentItem(SunatDocumentRequest request) {
        // Crear comprobante básico
        Comprobante.ComprobanteBuilder comprobanteBuilder = Comprobante.builder()
                .tipoComprobante(determinarTipoComprobante(request.getSerie()))
                .serieNumero(request.getSerie() + "-" + request.getNumero());
        
        // Configurar cliente si está disponible
        if (request.getCliente() != null) {
            comprobanteBuilder.cliente(convertToXBuilderCliente(request.getCliente()));
        }
        
        // Configurar impuestos (valores por defecto)
        ComprobanteImpuestos impuestos = ComprobanteImpuestos.builder()
                .igv(new BigDecimal("18.00"))
                .build();
        comprobanteBuilder.impuestos(impuestos);
        
        // Configurar valor de venta (valores por defecto)
        ComprobanteValorVenta valorVenta = ComprobanteValorVenta.builder()
                .importeTotal(new BigDecimal("118.00"))
                .gravado(new BigDecimal("100.00"))
                .build();
        comprobanteBuilder.valorVenta(valorVenta);
        
        // Crear item del resumen
        return SummaryDocumentsItem.builder()
                .tipoOperacion(Catalog19.ADICIONAR.toString())
                .comprobante(comprobanteBuilder.build())
                .build();
    }

    /**
     * Determina el tipo de comprobante basado en la serie.
     */
    private String determinarTipoComprobante(String serie) {
        if (serie == null) {
            return Catalog1_Invoice.BOLETA.getCode();
        }
        
        String serieUpper = serie.toUpperCase();
        if (serieUpper.startsWith("B")) {
            return Catalog1_Invoice.BOLETA.getCode();
        } else if (serieUpper.startsWith("BC")) {
            return Catalog1.NOTA_CREDITO.getCode();
        } else if (serieUpper.startsWith("BD")) {
            return Catalog1.NOTA_DEBITO.getCode();
        } else {
            return Catalog1_Invoice.BOLETA.getCode(); // Por defecto
        }
    }

    /**
     * Convierte ProveedorInfo a Proveedor de XBuilder.
     */
    private Proveedor convertToXBuilderProveedor(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        Proveedor.ProveedorBuilder builder = Proveedor.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial());
        
        if (proveedorInfo.getNombreComercial() != null) {
            builder.nombreComercial(proveedorInfo.getNombreComercial());
        }
        
        return builder.build();
    }

    /**
     * Convierte ClienteInfo a Cliente de XBuilder.
     */
    private Cliente convertToXBuilderCliente(SunatDocumentRequest.ClienteInfo clienteInfo) {
        Cliente.ClienteBuilder builder = Cliente.builder()
                .tipoDocumentoIdentidad(clienteInfo.getTipoDocumentoIdentidad() != null ? 
                        clienteInfo.getTipoDocumentoIdentidad() : Catalog6.DNI.getCode())
                .numeroDocumentoIdentidad(clienteInfo.getNumeroDocumentoIdentidad())
                .nombre(clienteInfo.getNombre() != null ? clienteInfo.getNombre() : 
                        (clienteInfo.getRazonSocial() != null ? clienteInfo.getRazonSocial() : "Cliente"));
        
        return builder.build();
    }
}