package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog1_Invoice;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import io.github.project.openubl.xbuilder.content.models.sunat.baja.VoidedDocuments;
import io.github.project.openubl.xbuilder.content.models.sunat.baja.VoidedDocumentsItem;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;

/**
 * Servicio para el manejo de comunicaciones de baja (VoidedDocuments) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar comunicaciones de baja electrónicas.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VoidedDocumentsService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera una comunicación de baja XML firmada sin enviarla a SUNAT.
     *
     * @param request Datos de la comunicación de baja
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateVoidedDocumentsXML(SunatDocumentRequest request) {
        log.info("Generando XML para comunicación de baja {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder
            VoidedDocuments voidedDocuments = convertToXBuilderVoidedDocuments(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignVoidedDocumentXML(voidedDocuments);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "COMUNICACION_BAJA"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para comunicación de baja {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para comunicación de baja {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de comunicación de baja", e);
        }
    }

    /**
     * Genera y envía una comunicación de baja a SUNAT.
     * Las comunicaciones de baja se procesan de forma asíncrona y devuelven un ticket.
     *
     * @param request Datos de la comunicación de baja
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendVoidedDocuments(SunatDocumentRequest request) {
        log.info("Generando y enviando comunicación de baja {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateVoidedDocumentsXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT con manejo de tickets
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLWithTicketHandling(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            // Las comunicaciones de baja pueden devolver un ticket
            if (sunatResponse.getSunat() != null && sunatResponse.getSunat().getTicket() != null) {
                xmlResponse.setTicket(sunatResponse.getSunat().getTicket());
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.PENDING);
                xmlResponse.setMessage("Comunicación de baja enviada, procesándose en SUNAT");
                log.info("Comunicación de baja {}-{} enviada con ticket: {}", 
                        request.getSerie(), request.getNumero(), sunatResponse.getSunat().getTicket());
            } else if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Comunicación de baja enviada y aceptada por SUNAT");
                log.info("Comunicación de baja {}-{} enviada exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Comunicación de baja rechazada por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Comunicación de baja {}-{} rechazada por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando comunicación de baja {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando comunicación de baja a SUNAT", e);
        }
    }

    /**
     * Consulta el estado de un ticket de comunicación de baja.
     *
     * @param ticket El número de ticket
     * @param originalXmlContent El XML original para obtener la configuración
     * @return Respuesta con el estado del ticket
     */
    public SunatDocumentResponse verifyVoidedDocumentsTicket(String ticket, String originalXmlContent) {
        log.info("Consultando ticket de comunicación de baja: {}", ticket);
        
        try {
            byte[] xmlBytes = originalXmlContent.getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.verifyTicket(ticket, xmlBytes);
            
            SunatDocumentResponse response = SunatDocumentResponse.builder()
                    .ticket(ticket)
                    .sentToSunat(true)
                    .sunatStatus(sunatResponse.getStatus().toString())
                    .sunatMessage(documentSenderService.getErrorMessage(sunatResponse))
                    .build();
            
            if (documentSenderService.isSuccessResponse(sunatResponse)) {
                response.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                response.setMessage("Comunicación de baja procesada exitosamente por SUNAT");
                log.info("Ticket {} procesado exitosamente", ticket);
            } else {
                response.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                response.setMessage("Error en el procesamiento de la comunicación de baja");
                response.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Error en ticket {}: {}", ticket, response.getErrorMessage());
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("Error consultando ticket {}: {}", ticket, e.getMessage(), e);
            return SunatDocumentResponse.error(null, 
                    "Error consultando ticket de comunicación de baja", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto VoidedDocuments de XBuilder.
     *
     * @param request El request con los datos de la comunicación de baja
     * @return Objeto VoidedDocuments de XBuilder
     */
    private VoidedDocuments convertToXBuilderVoidedDocuments(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a VoidedDocuments XBuilder");
        
        VoidedDocuments.VoidedDocumentsBuilder voidedBuilder = VoidedDocuments.builder()
                .numero(request.getNumero().intValue())
                .fechaEmision(LocalDate.now())
                .fechaEmisionComprobantes(request.getFechaEmision() != null ? 
                        request.getFechaEmision() : LocalDate.now().minusDays(1));
        
        // Configurar proveedor
        if (request.getProveedor() != null) {
            voidedBuilder.proveedor(convertToXBuilderProveedor(request.getProveedor()));
        }
        
        // Para comunicaciones de baja, necesitamos crear items de los documentos a anular
        // Por ahora creamos un item de ejemplo basado en la serie y número del request
        VoidedDocumentsItem item = VoidedDocumentsItem.builder()
                .serie(request.getSerie())
                .numero(request.getNumero().intValue())
                .tipoComprobante(determinarTipoComprobante(request.getSerie()))
                .descripcionSustento(request.getObservaciones() != null ? 
                        request.getObservaciones() : "Anulación de comprobante")
                .build();
        
        voidedBuilder.comprobante(item);
        
        return voidedBuilder.build();
    }

    /**
     * Determina el tipo de comprobante basado en la serie.
     */
    private String determinarTipoComprobante(String serie) {
        if (serie == null) {
            return Catalog1_Invoice.FACTURA.getCode();
        }
        
        String serieUpper = serie.toUpperCase();
        if (serieUpper.startsWith("F")) {
            return Catalog1_Invoice.FACTURA.getCode();
        } else if (serieUpper.startsWith("B")) {
            return Catalog1_Invoice.BOLETA.getCode();
        } else if (serieUpper.startsWith("FC") || serieUpper.startsWith("BC")) {
            return "07"; // Nota de crédito
        } else if (serieUpper.startsWith("FD") || serieUpper.startsWith("BD")) {
            return "08"; // Nota de débito
        } else {
            return Catalog1_Invoice.FACTURA.getCode(); // Por defecto
        }
    }

    /**
     * Convierte ProveedorInfo a Proveedor de XBuilder.
     */
    private Proveedor convertToXBuilderProveedor(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        Proveedor.ProveedorBuilder builder = Proveedor.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial());
        
        if (proveedorInfo.getNombreComercial() != null) {
            builder.nombreComercial(proveedorInfo.getNombreComercial());
        }
        
        return builder.build();
    }
}