package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog1;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog23;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog6;
import io.github.project.openubl.xbuilder.content.models.common.Cliente;
import io.github.project.openubl.xbuilder.content.models.common.Contacto;
import io.github.project.openubl.xbuilder.content.models.common.Direccion;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import io.github.project.openubl.xbuilder.content.models.sunat.percepcionretencion.ComprobanteAfectado;
import io.github.project.openubl.xbuilder.content.models.sunat.percepcionretencion.PercepcionRetencionOperacion;
import io.github.project.openubl.xbuilder.content.models.sunat.percepcionretencion.Retention;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;

/**
 * Servicio para el manejo de retenciones (Retention) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar retenciones electrónicas.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RetentionService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera una retención XML firmada sin enviarla a SUNAT.
     *
     * @param request Datos de la retención
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateRetentionXML(SunatDocumentRequest request) {
        log.info("Generando XML para retención {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder
            Retention retention = convertToXBuilderRetention(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignRetentionXML(retention);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "RETENCION"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para retención {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para retención {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de retención", e);
        }
    }

    /**
     * Genera y envía una retención a SUNAT.
     *
     * @param request Datos de la retención
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendRetention(SunatDocumentRequest request) {
        log.info("Generando y enviando retención {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateRetentionXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLFile(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Retención enviada y aceptada por SUNAT");
                log.info("Retención {}-{} enviada exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Retención rechazada por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Retención {}-{} rechazada por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando retención {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando retención a SUNAT", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto Retention de XBuilder.
     *
     * @param request El request con los datos de la retención
     * @return Objeto Retention de XBuilder
     */
    private Retention convertToXBuilderRetention(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a Retention XBuilder");
        
        Retention.RetentionBuilder retentionBuilder = Retention.builder()
                .serie(request.getSerie())
                .numero(request.getNumero().intValue())
                .fechaEmision(request.getFechaEmision() != null ? request.getFechaEmision() : LocalDate.now());
        
        // Configurar régimen de retención (por defecto tasa 3%)
        retentionBuilder.tipoRegimen(Catalog23.TASA_TRES.getCode());
        retentionBuilder.tipoRegimenPorcentaje(Catalog23.TASA_TRES.getPercent());
        
        // Configurar montos (valores por defecto)
        retentionBuilder.importeTotalRetenido(new BigDecimal("10.00"));
        retentionBuilder.importeTotalPagado(new BigDecimal("200.00"));
        
        // Configurar proveedor (agente de retención)
        if (request.getProveedor() != null) {
            retentionBuilder.proveedor(convertToXBuilderProveedor(request.getProveedor()));
        }
        
        // Configurar cliente (proveedor al que se le retiene)
        if (request.getCliente() != null) {
            retentionBuilder.cliente(convertToXBuilderCliente(request.getCliente()));
        }
        
        // Crear operación de retención
        PercepcionRetencionOperacion operacion = createRetencionOperacion(request);
        retentionBuilder.operacion(operacion);
        
        return retentionBuilder.build();
    }

    /**
     * Crea una operación de retención basada en el request.
     */
    private PercepcionRetencionOperacion createRetencionOperacion(SunatDocumentRequest request) {
        // Crear comprobante afectado
        ComprobanteAfectado comprobante = ComprobanteAfectado.builder()
                .tipoComprobante(Catalog1.FACTURA.getCode())
                .serieNumero(request.getSerie() + "-" + request.getNumero())
                .fechaEmision(request.getFechaEmision() != null ? request.getFechaEmision() : LocalDate.now())
                .importeTotal(new BigDecimal("210.00"))
                .moneda(request.getMoneda() != null ? request.getMoneda() : "PEN")
                .build();
        
        // Crear operación
        return PercepcionRetencionOperacion.builder()
                .numeroOperacion(1)
                .fechaOperacion(request.getFechaEmision() != null ? request.getFechaEmision() : LocalDate.now())
                .importeOperacion(new BigDecimal("100.00"))
                .comprobante(comprobante)
                .build();
    }

    /**
     * Convierte ProveedorInfo a Proveedor de XBuilder.
     */
    private Proveedor convertToXBuilderProveedor(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        Proveedor.ProveedorBuilder builder = Proveedor.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial());
        
        if (proveedorInfo.getNombreComercial() != null) {
            builder.nombreComercial(proveedorInfo.getNombreComercial());
        }
        
        if (proveedorInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(proveedorInfo.getDireccion()));
        }
        
        if (proveedorInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(proveedorInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte ClienteInfo a Cliente de XBuilder.
     */
    private Cliente convertToXBuilderCliente(SunatDocumentRequest.ClienteInfo clienteInfo) {
        Cliente.ClienteBuilder builder = Cliente.builder()
                .tipoDocumentoIdentidad(clienteInfo.getTipoDocumentoIdentidad() != null ? 
                        clienteInfo.getTipoDocumentoIdentidad() : Catalog6.RUC.getCode())
                .numeroDocumentoIdentidad(clienteInfo.getNumeroDocumentoIdentidad())
                .nombre(clienteInfo.getNombre() != null ? clienteInfo.getNombre() : 
                        (clienteInfo.getRazonSocial() != null ? clienteInfo.getRazonSocial() : "Proveedor"));
        
        if (clienteInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(clienteInfo.getDireccion()));
        }
        
        if (clienteInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(clienteInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte DireccionInfo a Direccion de XBuilder.
     */
    private Direccion convertToXBuilderDireccion(SunatDocumentRequest.DireccionInfo direccionInfo) {
        return Direccion.builder()
                .ubigeo(direccionInfo.getUbigeo())
                .departamento(direccionInfo.getDepartamento())
                .provincia(direccionInfo.getProvincia())
                .distrito(direccionInfo.getDistrito())
                .direccion(direccionInfo.getDireccion())
                .codigoLocal(direccionInfo.getCodigoLocal())
                .urbanizacion(direccionInfo.getUrbanizacion())
                .codigoPais(direccionInfo.getCodigoPais() != null ? direccionInfo.getCodigoPais() : "PE")
                .build();
    }

    /**
     * Convierte ContactoInfo a Contacto de XBuilder.
     */
    private Contacto convertToXBuilderContacto(SunatDocumentRequest.ContactoInfo contactoInfo) {
        return Contacto.builder()
                .telefono(contactoInfo.getTelefono())
                .email(contactoInfo.getEmail())
                .build();
    }
}