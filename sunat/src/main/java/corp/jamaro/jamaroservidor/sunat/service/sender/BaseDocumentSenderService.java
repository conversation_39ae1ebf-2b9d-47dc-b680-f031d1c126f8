package corp.jamaro.jamaroservidor.sunat.service.sender;

import io.github.project.openubl.xsender.Constants;
import io.github.project.openubl.xsender.camel.utils.CamelData;
import io.github.project.openubl.xsender.camel.utils.CamelUtils;
import io.github.project.openubl.xsender.company.CompanyCredentials;
import io.github.project.openubl.xsender.company.CompanyURLs;
import io.github.project.openubl.xsender.files.BillServiceFileAnalyzer;
import io.github.project.openubl.xsender.files.BillServiceXMLFileAnalyzer;
import io.github.project.openubl.xsender.files.ZipFile;
import io.github.project.openubl.xsender.models.SunatResponse;
import io.github.project.openubl.xsender.sunat.BillServiceDestination;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.CamelContext;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Servicio base para el envío de documentos XML a SUNAT usando XSender.
 * Proporciona funcionalidades comunes para el envío de todos los tipos de documentos.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaseDocumentSenderService {

    private final CamelContext camelContext;
    private final CompanyURLs companyURLs;
    private final CompanyCredentials companyCredentials;

    /**
     * Envía un archivo XML a SUNAT.
     *
     * @param xmlContent El contenido XML como bytes
     * @return Respuesta de SUNAT
     * @throws Exception Si ocurre un error durante el envío
     */
    public SunatResponse sendXMLFile(byte[] xmlContent) throws Exception {
        log.info("Enviando archivo XML a SUNAT");
        
        // Analizar el archivo XML
        BillServiceFileAnalyzer fileAnalyzer = new BillServiceXMLFileAnalyzer(xmlContent, companyURLs);
        
        // Obtener archivo ZIP
        ZipFile zipFile = fileAnalyzer.getZipFile();
        log.debug("Archivo ZIP creado exitosamente");
        
        // Configuración para enviar XML
        BillServiceDestination fileDestination = fileAnalyzer.getSendFileDestination();
        
        // Preparar datos para Camel
        CamelData camelData = CamelUtils.getBillServiceCamelData(zipFile, fileDestination, companyCredentials);
        
        // Enviar archivo
        SunatResponse response = camelContext.createProducerTemplate()
                .requestBodyAndHeaders(
                        Constants.XSENDER_BILL_SERVICE_URI,
                        camelData.getBody(),
                        camelData.getHeaders(),
                        SunatResponse.class
                );
        
        log.info("Respuesta de SUNAT recibida. Status: {}", response.getStatus());
        return response;
    }

    /**
     * Envía un archivo XML desde una ruta específica.
     *
     * @param xmlFilePath Ruta del archivo XML
     * @return Respuesta de SUNAT
     * @throws Exception Si ocurre un error durante el envío
     */
    public SunatResponse sendXMLFile(String xmlFilePath) throws Exception {
        log.info("Enviando archivo XML desde ruta: {}", xmlFilePath);
        
        Path xmlPath = Paths.get(xmlFilePath);
        
        // Analizar el archivo XML
        BillServiceFileAnalyzer fileAnalyzer = new BillServiceXMLFileAnalyzer(xmlPath, companyURLs);
        
        // Obtener archivo ZIP
        ZipFile zipFile = fileAnalyzer.getZipFile();
        log.debug("Archivo ZIP creado exitosamente");
        
        // Configuración para enviar XML
        BillServiceDestination fileDestination = fileAnalyzer.getSendFileDestination();
        
        // Preparar datos para Camel
        CamelData camelData = CamelUtils.getBillServiceCamelData(zipFile, fileDestination, companyCredentials);
        
        // Enviar archivo
        SunatResponse response = camelContext.createProducerTemplate()
                .requestBodyAndHeaders(
                        Constants.XSENDER_BILL_SERVICE_URI,
                        camelData.getBody(),
                        camelData.getHeaders(),
                        SunatResponse.class
                );
        
        log.info("Respuesta de SUNAT recibida. Status: {}", response.getStatus());
        return response;
    }

    /**
     * Consulta el estado de un ticket en SUNAT.
     * Utilizado para documentos como bajas y resúmenes diarios que requieren consulta posterior.
     *
     * @param ticket El número de ticket a consultar
     * @param xmlContent El contenido XML original para obtener la configuración de destino
     * @return Respuesta de SUNAT con el estado del ticket
     * @throws Exception Si ocurre un error durante la consulta
     */
    public SunatResponse verifyTicket(String ticket, byte[] xmlContent) throws Exception {
        log.info("Consultando ticket: {}", ticket);
        
        // Analizar el archivo XML para obtener la configuración de destino
        BillServiceFileAnalyzer fileAnalyzer = new BillServiceXMLFileAnalyzer(xmlContent, companyURLs);
        
        // Configuración para consultar ticket
        BillServiceDestination ticketDestination = fileAnalyzer.getVerifyTicketDestination();
        
        // Preparar datos para Camel
        CamelData camelTicketData = CamelUtils.getBillServiceCamelData(ticket, ticketDestination, companyCredentials);
        
        // Consultar ticket
        SunatResponse response = camelContext.createProducerTemplate()
                .requestBodyAndHeaders(
                        Constants.XSENDER_BILL_SERVICE_URI,
                        camelTicketData.getBody(),
                        camelTicketData.getHeaders(),
                        SunatResponse.class
                );
        
        log.info("Respuesta de consulta de ticket recibida. Status: {}", response.getStatus());
        return response;
    }

    /**
     * Envía un XML y maneja automáticamente la consulta de ticket si es necesario.
     * 
     * @param xmlContent El contenido XML como bytes
     * @return Respuesta final de SUNAT
     * @throws Exception Si ocurre un error durante el proceso
     */
    public SunatResponse sendXMLWithTicketHandling(byte[] xmlContent) throws Exception {
        log.info("Enviando XML con manejo automático de tickets");
        
        // Enviar archivo inicial
        SunatResponse initialResponse = sendXMLFile(xmlContent);
        
        // Si la respuesta contiene un ticket, consultarlo
        if (initialResponse.getSunat() != null && initialResponse.getSunat().getTicket() != null) {
            String ticket = initialResponse.getSunat().getTicket();
            log.info("Documento enviado con ticket: {}. Consultando estado...", ticket);
            
            // Esperar un momento antes de consultar (recomendado por SUNAT)
            try {
                Thread.sleep(5000); // 5 segundos
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupción durante espera para consulta de ticket");
            }
            
            // Consultar el ticket
            return verifyTicket(ticket, xmlContent);
        }
        
        // Si no hay ticket, devolver la respuesta inicial
        return initialResponse;
    }

    /**
     * Verifica si una respuesta de SUNAT indica éxito.
     *
     * @param response La respuesta de SUNAT
     * @return true si la respuesta indica éxito, false en caso contrario
     */
    public boolean isSuccessResponse(SunatResponse response) {
        if (response == null) {
            return false;
        }
        
        // Verificar el status general
        if (response.getStatus() == null) {
            return false;
        }
        
        // Los status exitosos típicamente son "OK" o similares
        String status = response.getStatus().toString().toUpperCase();
        boolean isSuccess = status.contains("OK") || status.contains("SUCCESS") || status.contains("ACEPTAD");
        
        log.debug("Verificando respuesta SUNAT. Status: {}, Es exitoso: {}", response.getStatus(), isSuccess);
        return isSuccess;
    }

    /**
     * Obtiene el mensaje de error de una respuesta de SUNAT.
     *
     * @param response La respuesta de SUNAT
     * @return El mensaje de error o información básica de la respuesta
     */
    public String getErrorMessage(SunatResponse response) {
        if (response == null) {
            return "Respuesta nula de SUNAT";
        }
        
        // Información básica de la respuesta
        StringBuilder message = new StringBuilder();
        message.append("Status: ").append(response.getStatus());
        
        if (response.getSunat() != null) {
            message.append(", SUNAT info disponible");
        }
        
        return message.toString();
    }
}