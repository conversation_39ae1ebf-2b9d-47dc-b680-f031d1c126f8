package corp.jamaro.jamaroservidor.sunat.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO para respuestas de operaciones SUNAT.
 * Contiene información sobre el resultado de generar y/o enviar documentos a SUNAT.
 */
@Data
@Builder
public class SunatDocumentResponse {
    
    // ID del comprobante en la base de datos
    private UUID comprobanteId;
    
    // Información del documento
    private String serie;
    private Long numero;
    private String tipoDocumento;
    
    // Estado de la operación
    private OperationStatus status;
    private String message;
    private LocalDateTime timestamp;
    
    // XML generado
    private String xmlContent;
    private boolean xmlSigned;
    
    // Información de envío a SUNAT
    private boolean sentToSunat;
    private String sunatStatus;
    private String sunatMessage;
    private String ticket;
    private byte[] cdr; // Constancia de Recepción
    
    // Información de errores
    private String errorCode;
    private String errorMessage;
    private Exception exception;
    
    /**
     * Estados posibles de la operación.
     */
    public enum OperationStatus {
        SUCCESS,
        ERROR,
        WARNING,
        PENDING,
        PROCESSING
    }
    
    /**
     * Crea una respuesta exitosa.
     */
    public static SunatDocumentResponse success(UUID comprobanteId, String serie, Long numero, String tipoDocumento) {
        return SunatDocumentResponse.builder()
                .comprobanteId(comprobanteId)
                .serie(serie)
                .numero(numero)
                .tipoDocumento(tipoDocumento)
                .status(OperationStatus.SUCCESS)
                .message("Operación completada exitosamente")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Crea una respuesta de error.
     */
    public static SunatDocumentResponse error(UUID comprobanteId, String message, Exception exception) {
        return SunatDocumentResponse.builder()
                .comprobanteId(comprobanteId)
                .status(OperationStatus.ERROR)
                .message(message)
                .errorMessage(exception != null ? exception.getMessage() : message)
                .exception(exception)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Crea una respuesta pendiente (para documentos con ticket).
     */
    public static SunatDocumentResponse pending(UUID comprobanteId, String serie, Long numero, String ticket) {
        return SunatDocumentResponse.builder()
                .comprobanteId(comprobanteId)
                .serie(serie)
                .numero(numero)
                .status(OperationStatus.PENDING)
                .message("Documento enviado, esperando procesamiento de SUNAT")
                .ticket(ticket)
                .sentToSunat(true)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Verifica si la operación fue exitosa.
     */
    public boolean isSuccess() {
        return status == OperationStatus.SUCCESS;
    }
    
    /**
     * Verifica si hubo un error.
     */
    public boolean isError() {
        return status == OperationStatus.ERROR;
    }
    
    /**
     * Verifica si la operación está pendiente.
     */
    public boolean isPending() {
        return status == OperationStatus.PENDING;
    }
}