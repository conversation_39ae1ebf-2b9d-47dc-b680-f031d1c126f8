package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog6;
import io.github.project.openubl.xbuilder.content.models.common.Cliente;
import io.github.project.openubl.xbuilder.content.models.common.Contacto;
import io.github.project.openubl.xbuilder.content.models.common.Direccion;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import io.github.project.openubl.xbuilder.content.models.standard.general.CreditNote;
import io.github.project.openubl.xbuilder.content.models.standard.general.DocumentoVentaDetalle;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servicio para el manejo de notas de crédito (CreditNote) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar notas de crédito electrónicas.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CreditNoteService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera una nota de crédito XML firmada sin enviarla a SUNAT.
     *
     * @param request Datos de la nota de crédito
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateCreditNoteXML(SunatDocumentRequest request) {
        log.info("Generando XML para nota de crédito {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder
            CreditNote creditNote = convertToXBuilderCreditNote(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignCreditNoteXML(creditNote);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "NOTA_CREDITO"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para nota de crédito {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para nota de crédito {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de nota de crédito", e);
        }
    }

    /**
     * Genera y envía una nota de crédito a SUNAT.
     *
     * @param request Datos de la nota de crédito
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendCreditNote(SunatDocumentRequest request) {
        log.info("Generando y enviando nota de crédito {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateCreditNoteXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLFile(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Nota de crédito enviada y aceptada por SUNAT");
                log.info("Nota de crédito {}-{} enviada exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Nota de crédito rechazada por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Nota de crédito {}-{} rechazada por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando nota de crédito {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando nota de crédito a SUNAT", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto CreditNote de XBuilder.
     *
     * @param request El request con los datos de la nota de crédito
     * @return Objeto CreditNote de XBuilder
     */
    private CreditNote convertToXBuilderCreditNote(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a CreditNote XBuilder");
        
        CreditNote.CreditNoteBuilder creditNoteBuilder = CreditNote.builder()
                .serie(request.getSerie())
                .numero(request.getNumero().intValue())
                .fechaEmision(request.getFechaEmision());
        
        // Configurar moneda si está especificada
        if (request.getMoneda() != null) {
            creditNoteBuilder.moneda(request.getMoneda());
        }
        
        // Las notas de crédito no tienen campo observaciones en XBuilder
        
        // Para notas de crédito, necesitamos información del comprobante afectado
        // Esto debería venir en el request, por ahora usamos valores por defecto
        String comprobanteAfectado = request.getSerie().replace("C", "") + "-" + (request.getNumero() - 1);
        creditNoteBuilder.comprobanteAfectadoSerieNumero(comprobanteAfectado);
        creditNoteBuilder.sustentoDescripcion("Anulación de comprobante");
        
        // Configurar proveedor
        if (request.getProveedor() != null) {
            creditNoteBuilder.proveedor(convertToXBuilderProveedor(request.getProveedor()));
        }
        
        // Configurar cliente
        if (request.getCliente() != null) {
            creditNoteBuilder.cliente(convertToXBuilderCliente(request.getCliente()));
        }
        
        // Configurar detalles
        if (request.getDetalles() != null && !request.getDetalles().isEmpty()) {
            List<DocumentoVentaDetalle> detalles = request.getDetalles().stream()
                    .map(this::convertToXBuilderDetalle)
                    .collect(Collectors.toList());
            
            for (DocumentoVentaDetalle detalle : detalles) {
                creditNoteBuilder.detalle(detalle);
            }
        }
        
        return creditNoteBuilder.build();
    }

    /**
     * Convierte ProveedorInfo a Proveedor de XBuilder.
     */
    private Proveedor convertToXBuilderProveedor(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        Proveedor.ProveedorBuilder builder = Proveedor.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial());
        
        if (proveedorInfo.getNombreComercial() != null) {
            builder.nombreComercial(proveedorInfo.getNombreComercial());
        }
        
        if (proveedorInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(proveedorInfo.getDireccion()));
        }
        
        if (proveedorInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(proveedorInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte ClienteInfo a Cliente de XBuilder.
     */
    private Cliente convertToXBuilderCliente(SunatDocumentRequest.ClienteInfo clienteInfo) {
        Cliente.ClienteBuilder builder = Cliente.builder()
                .tipoDocumentoIdentidad(clienteInfo.getTipoDocumentoIdentidad() != null ? 
                        clienteInfo.getTipoDocumentoIdentidad() : Catalog6.RUC.getCode())
                .numeroDocumentoIdentidad(clienteInfo.getNumeroDocumentoIdentidad())
                .nombre(clienteInfo.getNombre() != null ? clienteInfo.getNombre() : 
                        (clienteInfo.getRazonSocial() != null ? clienteInfo.getRazonSocial() : "Cliente"));
        
        if (clienteInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(clienteInfo.getDireccion()));
        }
        
        if (clienteInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(clienteInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte DireccionInfo a Direccion de XBuilder.
     */
    private Direccion convertToXBuilderDireccion(SunatDocumentRequest.DireccionInfo direccionInfo) {
        return Direccion.builder()
                .ubigeo(direccionInfo.getUbigeo())
                .departamento(direccionInfo.getDepartamento())
                .provincia(direccionInfo.getProvincia())
                .distrito(direccionInfo.getDistrito())
                .direccion(direccionInfo.getDireccion())
                .codigoLocal(direccionInfo.getCodigoLocal())
                .urbanizacion(direccionInfo.getUrbanizacion())
                .codigoPais(direccionInfo.getCodigoPais() != null ? direccionInfo.getCodigoPais() : "PE")
                .build();
    }

    /**
     * Convierte ContactoInfo a Contacto de XBuilder.
     */
    private Contacto convertToXBuilderContacto(SunatDocumentRequest.ContactoInfo contactoInfo) {
        return Contacto.builder()
                .telefono(contactoInfo.getTelefono())
                .email(contactoInfo.getEmail())
                .build();
    }

    /**
     * Convierte DetalleInfo a DocumentoVentaDetalle de XBuilder.
     */
    private DocumentoVentaDetalle convertToXBuilderDetalle(SunatDocumentRequest.DetalleInfo detalleInfo) {
        DocumentoVentaDetalle.DocumentoVentaDetalleBuilder builder = DocumentoVentaDetalle.builder()
                .descripcion(detalleInfo.getDescripcion())
                .cantidad(detalleInfo.getCantidad() != null ? new BigDecimal(detalleInfo.getCantidad().toString()) : BigDecimal.ONE)
                .precio(detalleInfo.getPrecio() != null ? new BigDecimal(detalleInfo.getPrecio().toString()) : BigDecimal.ZERO);
        
        if (detalleInfo.getUnidadMedida() != null) {
            builder.unidadMedida(detalleInfo.getUnidadMedida());
        }
        
        if (detalleInfo.getPrecioConImpuestos() != null) {
            builder.precioConImpuestos(detalleInfo.getPrecioConImpuestos());
        }
        
        if (detalleInfo.getIgvTipo() != null) {
            builder.igvTipo(detalleInfo.getIgvTipo());
        }
        
        if (detalleInfo.getIscTipo() != null) {
            builder.iscTipo(detalleInfo.getIscTipo());
        }
        
        if (detalleInfo.getTasaIsc() != null) {
            builder.tasaIsc(new BigDecimal(detalleInfo.getTasaIsc().toString()));
        }
        
        if (detalleInfo.getIcbAplica() != null) {
            builder.icbAplica(detalleInfo.getIcbAplica());
        }
        
        return builder.build();
    }
}