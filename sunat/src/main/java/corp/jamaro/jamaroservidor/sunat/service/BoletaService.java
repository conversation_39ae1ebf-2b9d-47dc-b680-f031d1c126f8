package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog1_Invoice;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog6;
import io.github.project.openubl.xbuilder.content.models.common.Cliente;
import io.github.project.openubl.xbuilder.content.models.common.Contacto;
import io.github.project.openubl.xbuilder.content.models.common.Direccion;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import io.github.project.openubl.xbuilder.content.models.standard.general.DocumentoVentaDetalle;
import io.github.project.openubl.xbuilder.content.models.standard.general.Invoice;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servicio para el manejo de boletas de venta (Invoice con tipo boleta) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar boletas electrónicas.
 * Las boletas son comprobantes dirigidos a consumidores finales.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BoletaService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera una boleta XML firmada sin enviarla a SUNAT.
     *
     * @param request Datos de la boleta
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateBoletaXML(SunatDocumentRequest request) {
        log.info("Generando XML para boleta {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder (Invoice con tipo boleta)
            Invoice boleta = convertToXBuilderBoleta(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignInvoiceXML(boleta);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "BOLETA"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para boleta {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para boleta {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de boleta", e);
        }
    }

    /**
     * Genera y envía una boleta a SUNAT.
     *
     * @param request Datos de la boleta
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendBoleta(SunatDocumentRequest request) {
        log.info("Generando y enviando boleta {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateBoletaXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLFile(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Boleta enviada y aceptada por SUNAT");
                log.info("Boleta {}-{} enviada exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Boleta rechazada por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Boleta {}-{} rechazada por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando boleta {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando boleta a SUNAT", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto Invoice de XBuilder configurado como boleta.
     *
     * @param request El request con los datos de la boleta
     * @return Objeto Invoice de XBuilder configurado como boleta
     */
    private Invoice convertToXBuilderBoleta(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a Invoice XBuilder (boleta)");
        
        Invoice.InvoiceBuilder invoiceBuilder = Invoice.builder()
                .serie(request.getSerie())
                .numero(request.getNumero().intValue())
                .fechaEmision(request.getFechaEmision())
                .tipoComprobante(Catalog1_Invoice.BOLETA.getCode()); // Configurar como boleta
        
        // Configurar moneda si está especificada
        if (request.getMoneda() != null) {
            invoiceBuilder.moneda(request.getMoneda());
        }
        
        // Configurar observaciones si están especificadas
        if (request.getObservaciones() != null) {
            invoiceBuilder.observaciones(request.getObservaciones());
        }
        
        // Configurar proveedor
        if (request.getProveedor() != null) {
            invoiceBuilder.proveedor(convertToXBuilderProveedor(request.getProveedor()));
        }
        
        // Configurar cliente (consumidor final)
        if (request.getCliente() != null) {
            invoiceBuilder.cliente(convertToXBuilderClienteBoleta(request.getCliente()));
        }
        
        // Configurar detalles
        if (request.getDetalles() != null && !request.getDetalles().isEmpty()) {
            List<DocumentoVentaDetalle> detalles = request.getDetalles().stream()
                    .map(this::convertToXBuilderDetalle)
                    .collect(Collectors.toList());
            
            for (DocumentoVentaDetalle detalle : detalles) {
                invoiceBuilder.detalle(detalle);
            }
        }
        
        return invoiceBuilder.build();
    }

    /**
     * Convierte ProveedorInfo a Proveedor de XBuilder.
     */
    private Proveedor convertToXBuilderProveedor(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        Proveedor.ProveedorBuilder builder = Proveedor.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial());
        
        if (proveedorInfo.getNombreComercial() != null) {
            builder.nombreComercial(proveedorInfo.getNombreComercial());
        }
        
        if (proveedorInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(proveedorInfo.getDireccion()));
        }
        
        if (proveedorInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(proveedorInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte ClienteInfo a Cliente de XBuilder específicamente para boletas.
     * Las boletas típicamente van dirigidas a consumidores finales con DNI o sin documento.
     */
    private Cliente convertToXBuilderClienteBoleta(SunatDocumentRequest.ClienteInfo clienteInfo) {
        Cliente.ClienteBuilder builder = Cliente.builder();
        
        // Para boletas, el tipo de documento típicamente es DNI o sin documento
        String tipoDocumento = clienteInfo.getTipoDocumentoIdentidad();
        if (tipoDocumento == null) {
            // Si no se especifica, usar DNI por defecto para boletas
            tipoDocumento = Catalog6.DNI.getCode();
        }
        builder.tipoDocumentoIdentidad(tipoDocumento);
        
        // Número de documento (puede ser vacío para "sin documento")
        String numeroDocumento = clienteInfo.getNumeroDocumentoIdentidad();
        if (numeroDocumento == null || numeroDocumento.trim().isEmpty()) {
            numeroDocumento = "-"; // Valor por defecto para sin documento
        }
        builder.numeroDocumentoIdentidad(numeroDocumento);
        
        // Nombre del cliente
        String nombre = clienteInfo.getNombre();
        if (nombre == null || nombre.trim().isEmpty()) {
            nombre = clienteInfo.getRazonSocial() != null ? 
                    clienteInfo.getRazonSocial() : "Cliente";
        }
        builder.nombre(nombre);
        
        if (clienteInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(clienteInfo.getDireccion()));
        }
        
        if (clienteInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(clienteInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte DireccionInfo a Direccion de XBuilder.
     */
    private Direccion convertToXBuilderDireccion(SunatDocumentRequest.DireccionInfo direccionInfo) {
        return Direccion.builder()
                .ubigeo(direccionInfo.getUbigeo())
                .departamento(direccionInfo.getDepartamento())
                .provincia(direccionInfo.getProvincia())
                .distrito(direccionInfo.getDistrito())
                .direccion(direccionInfo.getDireccion())
                .codigoLocal(direccionInfo.getCodigoLocal())
                .urbanizacion(direccionInfo.getUrbanizacion())
                .codigoPais(direccionInfo.getCodigoPais() != null ? direccionInfo.getCodigoPais() : "PE")
                .build();
    }

    /**
     * Convierte ContactoInfo a Contacto de XBuilder.
     */
    private Contacto convertToXBuilderContacto(SunatDocumentRequest.ContactoInfo contactoInfo) {
        return Contacto.builder()
                .telefono(contactoInfo.getTelefono())
                .email(contactoInfo.getEmail())
                .build();
    }

    /**
     * Convierte DetalleInfo a DocumentoVentaDetalle de XBuilder.
     */
    private DocumentoVentaDetalle convertToXBuilderDetalle(SunatDocumentRequest.DetalleInfo detalleInfo) {
        DocumentoVentaDetalle.DocumentoVentaDetalleBuilder builder = DocumentoVentaDetalle.builder()
                .descripcion(detalleInfo.getDescripcion())
                .cantidad(detalleInfo.getCantidad() != null ? new BigDecimal(detalleInfo.getCantidad().toString()) : BigDecimal.ONE)
                .precio(detalleInfo.getPrecio() != null ? new BigDecimal(detalleInfo.getPrecio().toString()) : BigDecimal.ZERO);
        
        if (detalleInfo.getUnidadMedida() != null) {
            builder.unidadMedida(detalleInfo.getUnidadMedida());
        }
        
        if (detalleInfo.getPrecioConImpuestos() != null) {
            builder.precioConImpuestos(detalleInfo.getPrecioConImpuestos());
        }
        
        if (detalleInfo.getIgvTipo() != null) {
            builder.igvTipo(detalleInfo.getIgvTipo());
        }
        
        if (detalleInfo.getIscTipo() != null) {
            builder.iscTipo(detalleInfo.getIscTipo());
        }
        
        if (detalleInfo.getTasaIsc() != null) {
            builder.tasaIsc(new BigDecimal(detalleInfo.getTasaIsc().toString()));
        }
        
        if (detalleInfo.getIcbAplica() != null) {
            builder.icbAplica(detalleInfo.getIcbAplica());
        }
        
        return builder.build();
    }
}