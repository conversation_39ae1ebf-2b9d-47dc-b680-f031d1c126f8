package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog6;
import io.github.project.openubl.xbuilder.content.models.common.Cliente;
import io.github.project.openubl.xbuilder.content.models.common.Contacto;
import io.github.project.openubl.xbuilder.content.models.common.Direccion;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import io.github.project.openubl.xbuilder.content.models.standard.general.DebitNote;
import io.github.project.openubl.xbuilder.content.models.standard.general.DocumentoVentaDetalle;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servicio para el manejo de notas de débito (DebitNote) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar notas de débito electrónicas.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DebitNoteService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera una nota de débito XML firmada sin enviarla a SUNAT.
     *
     * @param request Datos de la nota de débito
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateDebitNoteXML(SunatDocumentRequest request) {
        log.info("Generando XML para nota de débito {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder
            DebitNote debitNote = convertToXBuilderDebitNote(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignDebitNoteXML(debitNote);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "NOTA_DEBITO"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para nota de débito {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para nota de débito {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de nota de débito", e);
        }
    }

    /**
     * Genera y envía una nota de débito a SUNAT.
     *
     * @param request Datos de la nota de débito
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendDebitNote(SunatDocumentRequest request) {
        log.info("Generando y enviando nota de débito {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateDebitNoteXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLFile(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Nota de débito enviada y aceptada por SUNAT");
                log.info("Nota de débito {}-{} enviada exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Nota de débito rechazada por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Nota de débito {}-{} rechazada por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando nota de débito {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando nota de débito a SUNAT", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto DebitNote de XBuilder.
     *
     * @param request El request con los datos de la nota de débito
     * @return Objeto DebitNote de XBuilder
     */
    private DebitNote convertToXBuilderDebitNote(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a DebitNote XBuilder");
        
        DebitNote.DebitNoteBuilder debitNoteBuilder = DebitNote.builder()
                .serie(request.getSerie())
                .numero(request.getNumero().intValue())
                .fechaEmision(request.getFechaEmision());
        
        // Configurar moneda si está especificada
        if (request.getMoneda() != null) {
            debitNoteBuilder.moneda(request.getMoneda());
        }
        
        // Para notas de débito, necesitamos información del comprobante afectado
        // Esto debería venir en el request, por ahora usamos valores por defecto
        String comprobanteAfectado = request.getSerie().replace("D", "") + "-" + (request.getNumero() - 1);
        debitNoteBuilder.comprobanteAfectadoSerieNumero(comprobanteAfectado);
        debitNoteBuilder.sustentoDescripcion("Incremento por intereses o penalidades");
        
        // Configurar proveedor
        if (request.getProveedor() != null) {
            debitNoteBuilder.proveedor(convertToXBuilderProveedor(request.getProveedor()));
        }
        
        // Configurar cliente
        if (request.getCliente() != null) {
            debitNoteBuilder.cliente(convertToXBuilderCliente(request.getCliente()));
        }
        
        // Configurar detalles
        if (request.getDetalles() != null && !request.getDetalles().isEmpty()) {
            List<DocumentoVentaDetalle> detalles = request.getDetalles().stream()
                    .map(this::convertToXBuilderDetalle)
                    .collect(Collectors.toList());
            
            for (DocumentoVentaDetalle detalle : detalles) {
                debitNoteBuilder.detalle(detalle);
            }
        }
        
        return debitNoteBuilder.build();
    }

    /**
     * Convierte ProveedorInfo a Proveedor de XBuilder.
     */
    private Proveedor convertToXBuilderProveedor(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        Proveedor.ProveedorBuilder builder = Proveedor.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial());
        
        if (proveedorInfo.getNombreComercial() != null) {
            builder.nombreComercial(proveedorInfo.getNombreComercial());
        }
        
        if (proveedorInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(proveedorInfo.getDireccion()));
        }
        
        if (proveedorInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(proveedorInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte ClienteInfo a Cliente de XBuilder.
     */
    private Cliente convertToXBuilderCliente(SunatDocumentRequest.ClienteInfo clienteInfo) {
        Cliente.ClienteBuilder builder = Cliente.builder()
                .tipoDocumentoIdentidad(clienteInfo.getTipoDocumentoIdentidad() != null ? 
                        clienteInfo.getTipoDocumentoIdentidad() : Catalog6.RUC.getCode())
                .numeroDocumentoIdentidad(clienteInfo.getNumeroDocumentoIdentidad())
                .nombre(clienteInfo.getNombre() != null ? clienteInfo.getNombre() : 
                        (clienteInfo.getRazonSocial() != null ? clienteInfo.getRazonSocial() : "Cliente"));
        
        if (clienteInfo.getDireccion() != null) {
            builder.direccion(convertToXBuilderDireccion(clienteInfo.getDireccion()));
        }
        
        if (clienteInfo.getContacto() != null) {
            builder.contacto(convertToXBuilderContacto(clienteInfo.getContacto()));
        }
        
        return builder.build();
    }

    /**
     * Convierte DireccionInfo a Direccion de XBuilder.
     */
    private Direccion convertToXBuilderDireccion(SunatDocumentRequest.DireccionInfo direccionInfo) {
        return Direccion.builder()
                .ubigeo(direccionInfo.getUbigeo())
                .departamento(direccionInfo.getDepartamento())
                .provincia(direccionInfo.getProvincia())
                .distrito(direccionInfo.getDistrito())
                .direccion(direccionInfo.getDireccion())
                .codigoLocal(direccionInfo.getCodigoLocal())
                .urbanizacion(direccionInfo.getUrbanizacion())
                .codigoPais(direccionInfo.getCodigoPais() != null ? direccionInfo.getCodigoPais() : "PE")
                .build();
    }

    /**
     * Convierte ContactoInfo a Contacto de XBuilder.
     */
    private Contacto convertToXBuilderContacto(SunatDocumentRequest.ContactoInfo contactoInfo) {
        return Contacto.builder()
                .telefono(contactoInfo.getTelefono())
                .email(contactoInfo.getEmail())
                .build();
    }

    /**
     * Convierte DetalleInfo a DocumentoVentaDetalle de XBuilder.
     */
    private DocumentoVentaDetalle convertToXBuilderDetalle(SunatDocumentRequest.DetalleInfo detalleInfo) {
        DocumentoVentaDetalle.DocumentoVentaDetalleBuilder builder = DocumentoVentaDetalle.builder()
                .descripcion(detalleInfo.getDescripcion())
                .cantidad(detalleInfo.getCantidad() != null ? new BigDecimal(detalleInfo.getCantidad().toString()) : BigDecimal.ONE)
                .precio(detalleInfo.getPrecio() != null ? new BigDecimal(detalleInfo.getPrecio().toString()) : BigDecimal.ZERO);
        
        if (detalleInfo.getUnidadMedida() != null) {
            builder.unidadMedida(detalleInfo.getUnidadMedida());
        }
        
        if (detalleInfo.getPrecioConImpuestos() != null) {
            builder.precioConImpuestos(detalleInfo.getPrecioConImpuestos());
        }
        
        if (detalleInfo.getIgvTipo() != null) {
            builder.igvTipo(detalleInfo.getIgvTipo());
        }
        
        if (detalleInfo.getIscTipo() != null) {
            builder.iscTipo(detalleInfo.getIscTipo());
        }
        
        if (detalleInfo.getTasaIsc() != null) {
            builder.tasaIsc(new BigDecimal(detalleInfo.getTasaIsc().toString()));
        }
        
        if (detalleInfo.getIcbAplica() != null) {
            builder.icbAplica(detalleInfo.getIcbAplica());
        }
        
        return builder.build();
    }
}