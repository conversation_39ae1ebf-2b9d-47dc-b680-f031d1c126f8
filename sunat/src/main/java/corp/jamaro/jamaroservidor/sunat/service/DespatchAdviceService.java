package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog1;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog18;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog20;
import io.github.project.openubl.xbuilder.content.catalogs.Catalog6;
import io.github.project.openubl.xbuilder.content.models.standard.guia.DespatchAdvice;
import io.github.project.openubl.xbuilder.content.models.standard.guia.DespatchAdviceItem;
import io.github.project.openubl.xbuilder.content.models.standard.guia.Destinatario;
import io.github.project.openubl.xbuilder.content.models.standard.guia.Destino;
import io.github.project.openubl.xbuilder.content.models.standard.guia.Envio;
import io.github.project.openubl.xbuilder.content.models.standard.guia.Partida;
import io.github.project.openubl.xbuilder.content.models.standard.guia.Remitente;
import io.github.project.openubl.xsender.models.SunatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;

/**
 * Servicio para el manejo de guías de remisión (DespatchAdvice) en SUNAT.
 * Proporciona funcionalidades para generar, firmar y enviar guías de remisión electrónicas.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DespatchAdviceService {

    private final BaseDocumentBuilderService documentBuilderService;
    private final BaseDocumentSenderService documentSenderService;

    /**
     * Genera una guía de remisión XML firmada sin enviarla a SUNAT.
     *
     * @param request Datos de la guía de remisión
     * @return Respuesta con el XML generado
     */
    public SunatDocumentResponse generateDespatchAdviceXML(SunatDocumentRequest request) {
        log.info("Generando XML para guía de remisión {}-{}", request.getSerie(), request.getNumero());
        
        try {
            // Convertir request a modelo XBuilder
            DespatchAdvice despatchAdvice = convertToXBuilderDespatchAdvice(request);
            
            // Generar y firmar XML
            String signedXml = documentBuilderService.generateAndSignDespatchAdviceXML(despatchAdvice);
            
            // Crear respuesta exitosa
            SunatDocumentResponse response = SunatDocumentResponse.success(
                    request.getComprobanteId(),
                    request.getSerie(),
                    request.getNumero(),
                    "GUIA_REMISION"
            );
            
            response.setXmlContent(signedXml);
            response.setXmlSigned(true);
            
            log.info("XML generado exitosamente para guía de remisión {}-{}", request.getSerie(), request.getNumero());
            return response;
            
        } catch (Exception e) {
            log.error("Error generando XML para guía de remisión {}-{}: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error generando XML de guía de remisión", e);
        }
    }

    /**
     * Genera y envía una guía de remisión a SUNAT.
     * Las guías de remisión pueden requerir token de acceso especial.
     *
     * @param request Datos de la guía de remisión
     * @return Respuesta con el resultado del envío
     */
    public SunatDocumentResponse generateAndSendDespatchAdvice(SunatDocumentRequest request) {
        log.info("Generando y enviando guía de remisión {}-{} a SUNAT", request.getSerie(), request.getNumero());
        
        try {
            // Generar XML
            SunatDocumentResponse xmlResponse = generateDespatchAdviceXML(request);
            if (xmlResponse.isError()) {
                return xmlResponse;
            }
            
            // Enviar a SUNAT con manejo de tickets (las guías pueden usar tickets)
            byte[] xmlBytes = xmlResponse.getXmlContent().getBytes(StandardCharsets.UTF_8);
            SunatResponse sunatResponse = documentSenderService.sendXMLWithTicketHandling(xmlBytes);
            
            // Actualizar respuesta con información de SUNAT
            xmlResponse.setSentToSunat(true);
            xmlResponse.setSunatStatus(sunatResponse.getStatus().toString());
            xmlResponse.setSunatMessage(documentSenderService.getErrorMessage(sunatResponse));
            
            // Las guías de remisión pueden devolver un ticket
            if (sunatResponse.getSunat() != null && sunatResponse.getSunat().getTicket() != null) {
                xmlResponse.setTicket(sunatResponse.getSunat().getTicket());
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.PENDING);
                xmlResponse.setMessage("Guía de remisión enviada, procesándose en SUNAT");
                log.info("Guía de remisión {}-{} enviada con ticket: {}", 
                        request.getSerie(), request.getNumero(), sunatResponse.getSunat().getTicket());
            } else if (documentSenderService.isSuccessResponse(sunatResponse)) {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.SUCCESS);
                xmlResponse.setMessage("Guía de remisión enviada y aceptada por SUNAT");
                log.info("Guía de remisión {}-{} enviada exitosamente a SUNAT", request.getSerie(), request.getNumero());
            } else {
                xmlResponse.setStatus(SunatDocumentResponse.OperationStatus.ERROR);
                xmlResponse.setMessage("Guía de remisión rechazada por SUNAT");
                xmlResponse.setErrorMessage(documentSenderService.getErrorMessage(sunatResponse));
                log.warn("Guía de remisión {}-{} rechazada por SUNAT: {}", 
                        request.getSerie(), request.getNumero(), xmlResponse.getErrorMessage());
            }
            
            return xmlResponse;
            
        } catch (Exception e) {
            log.error("Error enviando guía de remisión {}-{} a SUNAT: {}", 
                    request.getSerie(), request.getNumero(), e.getMessage(), e);
            return SunatDocumentResponse.error(request.getComprobanteId(), 
                    "Error enviando guía de remisión a SUNAT", e);
        }
    }

    /**
     * Convierte un SunatDocumentRequest a un objeto DespatchAdvice de XBuilder.
     *
     * @param request El request con los datos de la guía de remisión
     * @return Objeto DespatchAdvice de XBuilder
     */
    private DespatchAdvice convertToXBuilderDespatchAdvice(SunatDocumentRequest request) {
        log.debug("Convirtiendo request a DespatchAdvice XBuilder");
        
        DespatchAdvice.DespatchAdviceBuilder despatchBuilder = DespatchAdvice.builder()
                .serie(request.getSerie())
                .numero(request.getNumero().intValue())
                .tipoComprobante(Catalog1.GUIA_REMISION_REMITENTE.getCode())
                .fechaEmision(request.getFechaEmision() != null ? request.getFechaEmision() : LocalDate.now());
        
        // Configurar remitente
        if (request.getProveedor() != null) {
            despatchBuilder.remitente(convertToXBuilderRemitente(request.getProveedor()));
        }
        
        // Configurar destinatario
        if (request.getCliente() != null) {
            despatchBuilder.destinatario(convertToXBuilderDestinatario(request.getCliente()));
        }
        
        // Configurar envío
        Envio envio = createEnvio(request);
        despatchBuilder.envio(envio);
        
        // Configurar detalles (bienes a transportar)
        if (request.getDetalles() != null && !request.getDetalles().isEmpty()) {
            for (SunatDocumentRequest.DetalleInfo detalle : request.getDetalles()) {
                DespatchAdviceItem item = convertToXBuilderItem(detalle);
                despatchBuilder.detalle(item);
            }
        } else {
            // Crear un item por defecto
            DespatchAdviceItem defaultItem = DespatchAdviceItem.builder()
                    .cantidad(new BigDecimal("1.0"))
                    .unidadMedida("KG")
                    .codigo("ITEM001")
                    .descripcion("Mercadería general")
                    .build();
            despatchBuilder.detalle(defaultItem);
        }
        
        return despatchBuilder.build();
    }

    /**
     * Crea un objeto Envio para la guía de remisión.
     */
    private Envio createEnvio(SunatDocumentRequest request) {
        return Envio.builder()
                .tipoTraslado(Catalog20.TRASLADO_EMISOR_ITINERANTE_CP.getCode())
                .pesoTotal(BigDecimal.ONE)
                .pesoTotalUnidadMedida("KG")
                .transbordoProgramado(false)
                .tipoModalidadTraslado(Catalog18.TRANSPORTE_PRIVADO.getCode())
                .fechaTraslado(request.getFechaEmision() != null ? 
                        request.getFechaEmision() : LocalDate.now())
                .partida(Partida.builder()
                        .direccion("Dirección de origen")
                        .ubigeo("150101")
                        .build())
                .destino(Destino.builder()
                        .direccion("Dirección de destino")
                        .ubigeo("150102")
                        .build())
                .build();
    }

    /**
     * Convierte ProveedorInfo a Remitente de XBuilder.
     */
    private Remitente convertToXBuilderRemitente(SunatDocumentRequest.ProveedorInfo proveedorInfo) {
        return Remitente.builder()
                .ruc(proveedorInfo.getRuc())
                .razonSocial(proveedorInfo.getRazonSocial())
                .build();
    }

    /**
     * Convierte ClienteInfo a Destinatario de XBuilder.
     */
    private Destinatario convertToXBuilderDestinatario(SunatDocumentRequest.ClienteInfo clienteInfo) {
        return Destinatario.builder()
                .tipoDocumentoIdentidad(clienteInfo.getTipoDocumentoIdentidad() != null ? 
                        clienteInfo.getTipoDocumentoIdentidad() : Catalog6.DNI.getCode())
                .numeroDocumentoIdentidad(clienteInfo.getNumeroDocumentoIdentidad())
                .nombre(clienteInfo.getNombre() != null ? clienteInfo.getNombre() : 
                        (clienteInfo.getRazonSocial() != null ? clienteInfo.getRazonSocial() : "Destinatario"))
                .build();
    }

    /**
     * Convierte DetalleInfo a DespatchAdviceItem de XBuilder.
     */
    private DespatchAdviceItem convertToXBuilderItem(SunatDocumentRequest.DetalleInfo detalleInfo) {
        return DespatchAdviceItem.builder()
                .cantidad(detalleInfo.getCantidad() != null ? 
                        new BigDecimal(detalleInfo.getCantidad().toString()) : BigDecimal.ONE)
                .unidadMedida(detalleInfo.getUnidadMedida() != null ? 
                        detalleInfo.getUnidadMedida() : "KG")
                .codigo(detalleInfo.getCodigo() != null ? 
                        detalleInfo.getCodigo() : "ITEM001")
                .descripcion(detalleInfo.getDescripcion() != null ? 
                        detalleInfo.getDescripcion() : "Mercadería")
                .build();
    }
}