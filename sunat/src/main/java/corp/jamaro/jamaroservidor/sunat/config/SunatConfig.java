package corp.jamaro.jamaroservidor.sunat.config;

import io.github.project.openubl.xbuilder.enricher.config.DateProvider;
import io.github.project.openubl.xbuilder.enricher.config.Defaults;
import io.github.project.openubl.xsender.company.CompanyCredentials;
import io.github.project.openubl.xsender.company.CompanyURLs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Configuración principal para el módulo SUNAT.
 * Configura XBuilder y XSender para la generación y envío de documentos electrónicos.
 */
@Configuration
@ComponentScan("io.github.project.openubl.spring.xsender.runtime")
@Slf4j
public class SunatConfig {

    @Value("${sunat.ruc:12345678912}")
    private String ruc;

    @Value("${sunat.razon-social:Mi Empresa S.A.C.}")
    private String razonSocial;

    @Value("${sunat.username:MODDATOS}")
    private String username;

    @Value("${sunat.password:MODDATOS}")
    private String password;

    @Value("${sunat.token:}")
    private String token;

    @Value("${sunat.environment:beta}")
    private String environment;

    /**
     * Configuración por defecto para XBuilder.
     * Define las tasas de impuestos y otros valores por defecto.
     */
    @Bean
    public Defaults xbuilderDefaults() {
        log.info("Configurando XBuilder defaults...");
        return Defaults.builder()
                .icbTasa(new BigDecimal("0.2"))
                .igvTasa(new BigDecimal("0.18"))
                .build();
    }

    /**
     * Proveedor de fecha para XBuilder.
     * Permite personalizar la fecha utilizada en los documentos.
     */
    @Bean
    public DateProvider dateProvider() {
        return LocalDate::now;
    }

    /**
     * URLs de los servicios de SUNAT según el ambiente configurado.
     */
    @Bean
    public CompanyURLs companyURLs() {
        log.info("Configurando URLs de SUNAT para ambiente: {}", environment);
        
        if ("production".equalsIgnoreCase(environment)) {
            return CompanyURLs.builder()
                    .invoice("https://e-factura.sunat.gob.pe/ol-ti-itcpfegem/billService")
                    .perceptionRetention("https://e-factura.sunat.gob.pe/ol-ti-itemision-otroscpe-gem/billService")
                    .despatch("https://api-cpe.sunat.gob.pe/v1/contribuyente/gem")
                    .build();
        } else {
            // Beta/Testing environment
            return CompanyURLs.builder()
                    .invoice("https://e-beta.sunat.gob.pe/ol-ti-itcpfegem-beta/billService")
                    .perceptionRetention("https://e-beta.sunat.gob.pe/ol-ti-itemision-otroscpe-gem-beta/billService")
                    .despatch("https://api-cpe.sunat.gob.pe/v1/contribuyente/gem")
                    .build();
        }
    }

    /**
     * Credenciales de la empresa para SUNAT.
     */
    @Bean
    public CompanyCredentials companyCredentials() {
        log.info("Configurando credenciales SUNAT para RUC: {}", ruc);
        return CompanyCredentials.builder()
                .username(ruc + username)
                .password(password)
                .token(token)
                .build();
    }
}