# Configuración del módulo SUNAT
# ================================

# Habilitar/deshabilitar el módulo SUNAT
sunat.enabled=true

# Información de la empresa
sunat.ruc=12345678912
sunat.razon-social=Mi Empresa S.A.C.

# Credenciales SUNAT (Usuario SOL)
sunat.username=MODDATOS
sunat.password=MODDATOS

# Token para guías de remisión (opcional)
sunat.token=

# Ambiente SUNAT (beta o production)
sunat.environment=beta

# Configuración del certificado digital
sunat.certificate.path=LLAMA-PE-CERTIFICADO-DEMO-12345678912.pfx
sunat.certificate.password=password
sunat.signature.id=Project OpenUBL

# Configuración de logging para SUNAT
logging.level.corp.jamaro.jamaroservidor.sunat=DEBUG
logging.level.io.github.project.openubl=INFO
logging.level.org.apache.camel=WARN

# Configuración de Apache Camel (XSender)
camel.springboot.name=sunat-camel-context
camel.springboot.shutdown-timeout=30
camel.springboot.auto-startup=true