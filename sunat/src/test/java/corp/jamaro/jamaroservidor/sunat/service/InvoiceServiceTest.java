package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.models.standard.general.Invoice;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Tests para InvoiceService.
 * Verifica la funcionalidad de generación de facturas XML.
 */
@ExtendWith(MockitoExtension.class)
class InvoiceServiceTest {

    @Mock
    private BaseDocumentBuilderService documentBuilderService;

    @Mock
    private BaseDocumentSenderService documentSenderService;

    private InvoiceService invoiceService;

    @BeforeEach
    void setUp() {
        invoiceService = new InvoiceService(documentBuilderService, documentSenderService);
    }

    @Test
    void testGenerateInvoiceXML_Success() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createSampleInvoiceRequest(comprobanteId);
        String expectedXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Invoice>...</Invoice>";

        when(documentBuilderService.generateAndSignInvoiceXML(any(Invoice.class)))
                .thenReturn(expectedXml);

        // When
        SunatDocumentResponse response = invoiceService.generateInvoiceXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(comprobanteId, response.getComprobanteId());
        assertEquals("F001", response.getSerie());
        assertEquals(1L, response.getNumero());
        assertEquals("FACTURA", response.getTipoDocumento());
        assertEquals(expectedXml, response.getXmlContent());
        assertTrue(response.isXmlSigned());
        assertFalse(response.isSentToSunat());

        verify(documentBuilderService).generateAndSignInvoiceXML(any(Invoice.class));
    }

    @Test
    void testGenerateInvoiceXML_Error() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createSampleInvoiceRequest(comprobanteId);
        Exception expectedException = new RuntimeException("Error generando XML");

        when(documentBuilderService.generateAndSignInvoiceXML(any(Invoice.class)))
                .thenThrow(expectedException);

        // When
        SunatDocumentResponse response = invoiceService.generateInvoiceXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isError());
        assertEquals(comprobanteId, response.getComprobanteId());
        assertEquals("Error generando XML de factura", response.getMessage());
        assertEquals(expectedException, response.getException());
        assertNull(response.getXmlContent());

        verify(documentBuilderService).generateAndSignInvoiceXML(any(Invoice.class));
    }

    @Test
    void testConvertToXBuilderInvoice_CompleteData() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createCompleteInvoiceRequest(comprobanteId);
        String expectedXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Invoice>...</Invoice>";

        when(documentBuilderService.generateAndSignInvoiceXML(any(Invoice.class)))
                .thenReturn(expectedXml);

        // When
        SunatDocumentResponse response = invoiceService.generateInvoiceXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());

        // Verificar que se llamó al builder con los datos correctos
        verify(documentBuilderService).generateAndSignInvoiceXML(argThat(invoice -> {
            assertEquals("F001", invoice.getSerie());
            assertEquals(1, invoice.getNumero());
            assertEquals(LocalDate.of(2025, 7, 29), invoice.getFechaEmision());
            assertEquals("PEN", invoice.getMoneda());
            assertEquals("Observaciones de prueba", invoice.getObservaciones());
            
            // Verificar proveedor
            assertNotNull(invoice.getProveedor());
            assertEquals("12345678912", invoice.getProveedor().getRuc());
            assertEquals("Mi Empresa S.A.C.", invoice.getProveedor().getRazonSocial());
            
            // Verificar cliente
            assertNotNull(invoice.getCliente());
            assertEquals("98765432198", invoice.getCliente().getNumeroDocumentoIdentidad());
            assertEquals("Cliente Test S.A.", invoice.getCliente().getNombre());
            
            // Verificar detalles
            assertNotNull(invoice.getDetalles());
            assertEquals(2, invoice.getDetalles().size());
            
            return true;
        }));
    }

    @Test
    void testConvertToXBuilderInvoice_MinimalData() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createMinimalInvoiceRequest(comprobanteId);
        String expectedXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Invoice>...</Invoice>";

        when(documentBuilderService.generateAndSignInvoiceXML(any(Invoice.class)))
                .thenReturn(expectedXml);

        // When
        SunatDocumentResponse response = invoiceService.generateInvoiceXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());

        // Verificar que se llamó al builder con los datos mínimos
        verify(documentBuilderService).generateAndSignInvoiceXML(argThat(invoice -> {
            assertEquals("B001", invoice.getSerie());
            assertEquals(1, invoice.getNumero());
            assertNotNull(invoice.getFechaEmision());
            
            return true;
        }));
    }

    /**
     * Crea un request de factura con datos de ejemplo.
     */
    private SunatDocumentRequest createSampleInvoiceRequest(UUID comprobanteId) {
        return SunatDocumentRequest.builder()
                .comprobanteId(comprobanteId)
                .serie("F001")
                .numero(1L)
                .fechaEmision(LocalDate.of(2025, 7, 29))
                .moneda("PEN")
                .proveedor(SunatDocumentRequest.ProveedorInfo.builder()
                        .ruc("12345678912")
                        .razonSocial("Mi Empresa S.A.C.")
                        .build())
                .cliente(SunatDocumentRequest.ClienteInfo.builder()
                        .tipoDocumentoIdentidad("6")
                        .numeroDocumentoIdentidad("98765432198")
                        .nombre("Cliente Test")
                        .build())
                .detalles(Arrays.asList(
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Producto 1")
                                .cantidad(1.0)
                                .precio(100.0)
                                .unidadMedida("NIU")
                                .build(),
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Producto 2")
                                .cantidad(2.0)
                                .precio(50.0)
                                .unidadMedida("NIU")
                                .build()
                ))
                .build();
    }

    /**
     * Crea un request de factura con datos completos.
     */
    private SunatDocumentRequest createCompleteInvoiceRequest(UUID comprobanteId) {
        return SunatDocumentRequest.builder()
                .comprobanteId(comprobanteId)
                .serie("F001")
                .numero(1L)
                .fechaEmision(LocalDate.of(2025, 7, 29))
                .moneda("PEN")
                .observaciones("Observaciones de prueba")
                .proveedor(SunatDocumentRequest.ProveedorInfo.builder()
                        .ruc("12345678912")
                        .razonSocial("Mi Empresa S.A.C.")
                        .nombreComercial("Mi Empresa")
                        .direccion(SunatDocumentRequest.DireccionInfo.builder()
                                .ubigeo("150101")
                                .departamento("LIMA")
                                .provincia("LIMA")
                                .distrito("LIMA")
                                .direccion("AV. DIRECCION 123")
                                .codigoPais("PE")
                                .build())
                        .contacto(SunatDocumentRequest.ContactoInfo.builder()
                                .telefono("123456789")
                                .email("<EMAIL>")
                                .build())
                        .build())
                .cliente(SunatDocumentRequest.ClienteInfo.builder()
                        .tipoDocumentoIdentidad("6")
                        .numeroDocumentoIdentidad("98765432198")
                        .nombre("Cliente Test S.A.")
                        .razonSocial("Cliente Test S.A.")
                        .direccion(SunatDocumentRequest.DireccionInfo.builder()
                                .ubigeo("150102")
                                .departamento("LIMA")
                                .provincia("LIMA")
                                .distrito("MIRAFLORES")
                                .direccion("AV. CLIENTE 456")
                                .codigoPais("PE")
                                .build())
                        .contacto(SunatDocumentRequest.ContactoInfo.builder()
                                .telefono("987654321")
                                .email("<EMAIL>")
                                .build())
                        .build())
                .detalles(Arrays.asList(
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Producto Premium")
                                .cantidad(1.0)
                                .precio(100.0)
                                .unidadMedida("NIU")
                                .precioConImpuestos(false)
                                .icbAplica(true)
                                .build(),
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Servicio Especializado")
                                .cantidad(2.0)
                                .precio(75.0)
                                .unidadMedida("ZZ")
                                .precioConImpuestos(false)
                                .build()
                ))
                .build();
    }

    /**
     * Crea un request de factura con datos mínimos.
     */
    private SunatDocumentRequest createMinimalInvoiceRequest(UUID comprobanteId) {
        return SunatDocumentRequest.builder()
                .comprobanteId(comprobanteId)
                .serie("B001")
                .numero(1L)
                .fechaEmision(LocalDate.now())
                .proveedor(SunatDocumentRequest.ProveedorInfo.builder()
                        .ruc("12345678912")
                        .razonSocial("Mi Empresa S.A.C.")
                        .build())
                .cliente(SunatDocumentRequest.ClienteInfo.builder()
                        .numeroDocumentoIdentidad("12345678")
                        .nombre("Cliente")
                        .build())
                .detalles(Arrays.asList(
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Producto")
                                .cantidad(1.0)
                                .precio(10.0)
                                .build()
                ))
                .build();
    }
}