package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentRequest;
import corp.jamaro.jamaroservidor.sunat.model.SunatDocumentResponse;
import corp.jamaro.jamaroservidor.sunat.service.builder.BaseDocumentBuilderService;
import corp.jamaro.jamaroservidor.sunat.service.sender.BaseDocumentSenderService;
import io.github.project.openubl.xbuilder.content.models.standard.general.CreditNote;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Tests para CreditNoteService.
 * Verifica la funcionalidad de generación de notas de crédito XML.
 */
@ExtendWith(MockitoExtension.class)
class CreditNoteServiceTest {

    @Mock
    private BaseDocumentBuilderService documentBuilderService;

    @Mock
    private BaseDocumentSenderService documentSenderService;

    private CreditNoteService creditNoteService;

    @BeforeEach
    void setUp() {
        creditNoteService = new CreditNoteService(documentBuilderService, documentSenderService);
    }

    @Test
    void testGenerateCreditNoteXML_Success() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createSampleCreditNoteRequest(comprobanteId);
        String expectedXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><CreditNote>...</CreditNote>";

        when(documentBuilderService.generateAndSignCreditNoteXML(any(CreditNote.class)))
                .thenReturn(expectedXml);

        // When
        SunatDocumentResponse response = creditNoteService.generateCreditNoteXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(comprobanteId, response.getComprobanteId());
        assertEquals("FC01", response.getSerie());
        assertEquals(1L, response.getNumero());
        assertEquals("NOTA_CREDITO", response.getTipoDocumento());
        assertEquals(expectedXml, response.getXmlContent());
        assertTrue(response.isXmlSigned());
        assertFalse(response.isSentToSunat());

        verify(documentBuilderService).generateAndSignCreditNoteXML(any(CreditNote.class));
    }

    @Test
    void testGenerateCreditNoteXML_Error() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createSampleCreditNoteRequest(comprobanteId);
        Exception expectedException = new RuntimeException("Error generando XML");

        when(documentBuilderService.generateAndSignCreditNoteXML(any(CreditNote.class)))
                .thenThrow(expectedException);

        // When
        SunatDocumentResponse response = creditNoteService.generateCreditNoteXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isError());
        assertEquals(comprobanteId, response.getComprobanteId());
        assertEquals("Error generando XML de nota de crédito", response.getMessage());
        assertEquals(expectedException, response.getException());
        assertNull(response.getXmlContent());

        verify(documentBuilderService).generateAndSignCreditNoteXML(any(CreditNote.class));
    }

    @Test
    void testConvertToXBuilderCreditNote() throws Exception {
        // Given
        UUID comprobanteId = UUID.randomUUID();
        SunatDocumentRequest request = createSampleCreditNoteRequest(comprobanteId);
        String expectedXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><CreditNote>...</CreditNote>";

        when(documentBuilderService.generateAndSignCreditNoteXML(any(CreditNote.class)))
                .thenReturn(expectedXml);

        // When
        SunatDocumentResponse response = creditNoteService.generateCreditNoteXML(request);

        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());

        // Verificar que se llamó al builder con los datos correctos
        verify(documentBuilderService).generateAndSignCreditNoteXML(argThat(creditNote -> {
            assertEquals("FC01", creditNote.getSerie());
            assertEquals(1, creditNote.getNumero());
            assertEquals(LocalDate.of(2025, 7, 29), creditNote.getFechaEmision());
            assertEquals("PEN", creditNote.getMoneda());
            
            // Verificar proveedor
            assertNotNull(creditNote.getProveedor());
            assertEquals("12345678912", creditNote.getProveedor().getRuc());
            assertEquals("Mi Empresa S.A.C.", creditNote.getProveedor().getRazonSocial());
            
            // Verificar cliente
            assertNotNull(creditNote.getCliente());
            assertEquals("98765432198", creditNote.getCliente().getNumeroDocumentoIdentidad());
            assertEquals("Cliente Test", creditNote.getCliente().getNombre());
            
            // Verificar detalles
            assertNotNull(creditNote.getDetalles());
            assertEquals(2, creditNote.getDetalles().size());
            
            return true;
        }));
    }

    /**
     * Crea un request de nota de crédito con datos de ejemplo.
     */
    private SunatDocumentRequest createSampleCreditNoteRequest(UUID comprobanteId) {
        return SunatDocumentRequest.builder()
                .comprobanteId(comprobanteId)
                .serie("FC01")
                .numero(1L)
                .fechaEmision(LocalDate.of(2025, 7, 29))
                .moneda("PEN")
                .observaciones("Anulación por error")
                .proveedor(SunatDocumentRequest.ProveedorInfo.builder()
                        .ruc("12345678912")
                        .razonSocial("Mi Empresa S.A.C.")
                        .build())
                .cliente(SunatDocumentRequest.ClienteInfo.builder()
                        .tipoDocumentoIdentidad("6")
                        .numeroDocumentoIdentidad("98765432198")
                        .nombre("Cliente Test")
                        .build())
                .detalles(Arrays.asList(
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Producto 1")
                                .cantidad(1.0)
                                .precio(100.0)
                                .unidadMedida("NIU")
                                .build(),
                        SunatDocumentRequest.DetalleInfo.builder()
                                .descripcion("Producto 2")
                                .cantidad(2.0)
                                .precio(50.0)
                                .unidadMedida("NIU")
                                .build()
                ))
                .build();
    }
}