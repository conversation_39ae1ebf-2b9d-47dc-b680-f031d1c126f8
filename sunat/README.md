# Módulo SUNAT - Documentos Electrónicos para Perú

Este módulo proporciona funcionalidades completas para la generación y envío de documentos electrónicos a SUNAT (Superintendencia Nacional de Aduanas y de Administración Tributaria) de Perú.

## Características

### Documentos Soportados
- ✅ **Factura (Invoice)** - Implementado
- ✅ **Boleta (Invoice)** - Implementado
- ✅ **Nota de crédito (CreditNote)** - Implementado
- ✅ **Nota de débito (DebitNote)** - Implementado
- ✅ **Baja (VoidedDocuments)** - Implementado
- ✅ **Resumen diario (SummaryDocuments)** - Implementado
- ✅ **Percepción (Perception)** - Implementado
- ✅ **Retención (Retention)** - Implementado
- ✅ **Guía de remisión (DespatchDocument)** - Implementado

### Tecnologías Utilizadas
- **XBuilder 5.0.2** - Generación de XML UBL
- **XSender 5.0.2** - Envío de documentos a SUNAT
- **Spring Boot 3.4.0** - Framework de aplicación
- **Apache Camel** - Integración y enrutamiento
- **Lombok** - Reducción de código boilerplate

## Configuración

### 1. Dependencias

El módulo ya está configurado en el `build.gradle` principal. Las dependencias se gestionan automáticamente.

### 2. Propiedades de Configuración

Crea un archivo `application-sunat.properties` o agrega las siguientes propiedades a tu `application.properties`:

```properties
# Habilitar/deshabilitar el módulo SUNAT
sunat.enabled=true

# Información de la empresa
sunat.ruc=12345678912
sunat.razon-social=Mi Empresa S.A.C.

# Credenciales SUNAT (Usuario SOL)
sunat.username=MODDATOS
sunat.password=MODDATOS

# Token para guías de remisión (opcional)
sunat.token=

# Ambiente SUNAT (beta o production)
sunat.environment=beta

# Configuración del certificado digital
sunat.certificate.path=LLAMA-PE-CERTIFICADO-DEMO-12345678912.pfx
sunat.certificate.password=password
sunat.signature.id=Project OpenUBL
```

### 3. Certificado Digital

Coloca tu certificado digital (archivo .pfx) en el directorio `src/main/resources` del módulo SUNAT o en el classpath de tu aplicación.

## Uso

### Inyección de Dependencias

```java
@Service
public class MiServicioFacturacion {
    
    @Autowired
    private InvoiceService invoiceService;
    
    // ... tu código
}
```

### Generar XML de Factura

```java
// Crear request con datos de la factura
SunatDocumentRequest request = SunatDocumentRequest.builder()
    .comprobanteId(UUID.randomUUID())
    .serie("F001")
    .numero(1L)
    .fechaEmision(LocalDate.now())
    .moneda("PEN")
    .proveedor(SunatDocumentRequest.ProveedorInfo.builder()
        .ruc("12345678912")
        .razonSocial("Mi Empresa S.A.C.")
        .build())
    .cliente(SunatDocumentRequest.ClienteInfo.builder()
        .tipoDocumentoIdentidad("6")
        .numeroDocumentoIdentidad("98765432198")
        .nombre("Cliente Test")
        .build())
    .detalles(Arrays.asList(
        SunatDocumentRequest.DetalleInfo.builder()
            .descripcion("Producto 1")
            .cantidad(1.0)
            .precio(100.0)
            .unidadMedida("NIU")
            .build()
    ))
    .build();

// Generar XML firmado
SunatDocumentResponse response = invoiceService.generateInvoiceXML(request);

if (response.isSuccess()) {
    String xmlFirmado = response.getXmlContent();
    // Usar el XML generado
} else {
    // Manejar error
    String error = response.getErrorMessage();
}
```

### Generar y Enviar Factura a SUNAT

```java
// Generar y enviar directamente a SUNAT
SunatDocumentResponse response = invoiceService.generateAndSendInvoice(request);

if (response.isSuccess()) {
    System.out.println("Factura enviada exitosamente a SUNAT");
    System.out.println("Status SUNAT: " + response.getSunatStatus());
} else {
    System.out.println("Error: " + response.getErrorMessage());
}
```

## Arquitectura del Módulo

### Estructura de Paquetes

```
corp.jamaro.jamaroservidor.sunat/
├── config/
│   ├── SunatConfig.java              # Configuración principal
│   └── SunatAutoConfiguration.java   # Auto-configuración Spring
├── model/
│   ├── SunatDocumentRequest.java     # DTO de entrada
│   └── SunatDocumentResponse.java    # DTO de respuesta
├── service/
│   ├── InvoiceService.java           # Servicio de facturas
│   ├── builder/
│   │   └── BaseDocumentBuilderService.java  # Generación XML
│   └── sender/
│       └── BaseDocumentSenderService.java   # Envío a SUNAT
└── resources/
    └── application-sunat.properties  # Configuración ejemplo
```

### Componentes Principales

1. **SunatConfig**: Configuración de XBuilder y XSender
2. **BaseDocumentBuilderService**: Generación y firma de XML usando XBuilder
3. **BaseDocumentSenderService**: Envío de documentos a SUNAT usando XSender
4. **InvoiceService**: Servicio específico para facturas
5. **SunatDocumentRequest/Response**: DTOs para comunicación

## Flujo de Procesamiento

1. **Entrada**: Se recibe un `SunatDocumentRequest` con los datos del documento
2. **Conversión**: Se convierte el DTO a modelos XBuilder
3. **Enriquecimiento**: XBuilder calcula automáticamente totales e impuestos
4. **Generación XML**: Se genera el XML UBL usando plantillas Qute
5. **Firma Digital**: Se firma el XML con el certificado configurado
6. **Envío**: Se envía el XML a SUNAT usando Apache Camel
7. **Respuesta**: Se retorna un `SunatDocumentResponse` con el resultado

## Testing

El módulo incluye tests unitarios completos:

```bash
# Ejecutar tests del módulo SUNAT
./gradlew :sunat:test
```

### Ejemplo de Test

```java
@Test
void testGenerateInvoiceXML_Success() throws Exception {
    // Given
    SunatDocumentRequest request = createSampleInvoiceRequest();
    
    // When
    SunatDocumentResponse response = invoiceService.generateInvoiceXML(request);
    
    // Then
    assertTrue(response.isSuccess());
    assertNotNull(response.getXmlContent());
    assertTrue(response.isXmlSigned());
}
```

## Ambientes

### Ambiente Beta (Pruebas)
- URL: `https://e-beta.sunat.gob.pe/ol-ti-itcpfegem-beta/billService`
- Credenciales: Usuario y clave SOL de prueba
- Certificado: Certificado de prueba

### Ambiente Producción
- URL: `https://e-factura.sunat.gob.pe/ol-ti-itcpfegem/billService`
- Credenciales: Usuario y clave SOL reales
- Certificado: Certificado digital válido

## Próximos Pasos

1. **Implementar servicios restantes**: CreditNoteService, DebitNoteService, etc.
2. **Agregar validaciones**: Validación de datos antes de generar XML
3. **Mejorar manejo de errores**: Códigos de error específicos de SUNAT
4. **Agregar cache**: Cache de XMLs generados y respuestas de SUNAT
5. **Implementar retry**: Reintentos automáticos en caso de fallas de red
6. **Agregar métricas**: Monitoreo de envíos exitosos/fallidos

## Soporte

Para soporte y documentación adicional:
- [Documentación XBuilder](https://github.com/project-openubl/xbuilder)
- [Documentación XSender](https://github.com/project-openubl/xsender)
- [Documentación SUNAT](https://cpe.sunat.gob.pe/)

## Licencia

Este módulo es parte del proyecto JamaroServidor y sigue la misma licencia del proyecto principal.